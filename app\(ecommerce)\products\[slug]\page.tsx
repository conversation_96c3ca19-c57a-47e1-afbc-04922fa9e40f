"use client";

import { addToCart, addToWishlist } from "@/actions/cart-actions";
import SubmitButton from "@/components/submitButton";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { product } from "@/data/product";
import Link from "next/link";
import {
  ArrowRight,
  Check,
  ChevronRight,
  Clock,
  Eye,
  Heart,
  HelpCircle,
  MessageCircle,
  Share2,
  ShoppingBag,
  ShoppingCart,
  Star,
  Truck,
  Zap,
  AlertCircle,
  Calendar,
  Thermometer,
  Pill,
  ShieldAlert,
  Award,
  Leaf,
  HeartPulse,
  UserCog,
} from "lucide-react";
import { useState, useEffect } from "react";
import { Tab, TabList, TabPanel, Tabs } from "react-aria-components";
import { useFormState, useFormStatus } from "react-dom";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { useCart } from "@/lib/CartContext";

// Custom AddToCartButton component
function AddToCartButton({ product, quantity, selectedSize, selectedColor }) {
  const { addItem } = useCart();
  const { toast } = useToast();
  const { pending } = useFormStatus();

  const handleAddToCart = async () => {
    if (!selectedSize && product.sizes && product.sizes.length > 0) {
      toast({
        title: "Please select a size",
        description: "You need to select a size before adding to cart",
        variant: "destructive",
      });
      return;
    }

    if (!selectedColor && product.colors && product.colors.length > 0) {
      toast({
        title: "Please select a color",
        description: "You need to select a color before adding to cart",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await addToCart({
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: quantity,
        image: product.image,
        size: selectedSize,
        color: selectedColor,
      });

      if (result.success) {
        // Add to client-side cart
        addItem({
          id: result.product.id,
          productId: result.product.productId,
          name: result.product.name,
          price: result.product.price,
          quantity: result.product.quantity,
          image: result.product.image,
          size: result.product.size,
          color: result.product.color,
        });

        toast({
          title: "Added to cart",
          description: `${product.name} has been added to your cart`,
        });
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add product to cart",
        variant: "destructive",
      });
    }
  };

  return (
    <Button
      onClick={handleAddToCart}
      disabled={pending}
      data-add-to-cart
      className="w-full py-6 bg-black hover:bg-gray-800 text-white flex items-center justify-center gap-2 rounded-md"
    >
      <ShoppingBag className="w-5 h-5" />
      {pending ? "Adding..." : "Add to Cart"}
    </Button>
  );
}

export default function Product({ params }: any) {
  const targetProduct = product.filter((p) => p.slug === params.slug)[0];
  const [currentImage, setCurrentImage] = useState(targetProduct.image);
  const [cartCount, setCartCount] = useState(1);
  const [viewersCount, setViewersCount] = useState(
    Math.floor(Math.random() * 30) + 5
  );
  const [timeLeft, setTimeLeft] = useState(24 * 60 * 60); // 24 hours in seconds
  const [selectedSize, setSelectedSize] = useState("");
  const [selectedColor, setSelectedColor] = useState("");
  const { toast } = useToast();
  const { addToWishlist } = useCart();

  // Format time for countdown display
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate random viewers
  useEffect(() => {
    const interval = setInterval(() => {
      const change = Math.random() > 0.5 ? 1 : -1;
      setViewersCount((prev) => Math.max(5, prev + change));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Filter similar products (same category or price range)
  const similarProducts = product
    .filter(
      (p) =>
        p.id !== targetProduct.id &&
        Math.abs(p.price - targetProduct.price) < 20
    )
    .slice(0, 4);

  return (
    <div className="bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-3 max-w-7xl">
          <div className="flex items-center text-sm text-gray-500">
            <Link href="/" className="hover:text-black transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link
              href="/products"
              className="hover:text-black transition-colors"
            >
              Products
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">
              {targetProduct.name}
            </span>
          </div>
        </div>
      </div>

      {/* Main Product Section */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Product Images - Left Column */}
            <div className="lg:w-1/2 p-6 border-r border-gray-100">
              {/* Desktop View */}
              <div className="hidden md:block">
                {/* Main Image with Hover Zoom */}
                <div className="relative mb-6 h-[500px] bg-gray-50 rounded-xl overflow-hidden">
                  <div className="w-full h-full group">
                    <img
                      src={currentImage}
                      alt={targetProduct.name}
                      className="w-full h-full object-contain p-4 transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-5 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <span className="bg-white bg-opacity-75 text-black text-xs px-2 py-1 rounded">
                        Hover to zoom
                      </span>
                    </div>
                  </div>

                  {/* Product Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    {targetProduct.price > 80 && (
                      <Badge className="bg-black text-white px-3 py-1 text-sm font-medium rounded-full">
                        PREMIUM
                      </Badge>
                    )}
                    <Badge className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                      NEW
                    </Badge>
                    {targetProduct.price > 90 && (
                      <Badge className="bg-amber-500 text-white px-3 py-1 text-sm font-medium rounded-full">
                        BESTSELLER
                      </Badge>
                    )}
                  </div>

                  {/* Discount Badge */}
                  <Badge className="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                    -20%
                  </Badge>
                </div>

                {/* Thumbnails */}
                <div className="grid grid-cols-5 gap-4 h-24">
                  <div
                    onClick={() => setCurrentImage(targetProduct.image)}
                    className={`border rounded-lg cursor-pointer overflow-hidden ${
                      currentImage === targetProduct.image
                        ? "ring-2 ring-black"
                        : "hover:border-gray-400"
                    }`}
                  >
                    <img
                      src={targetProduct.image}
                      alt="Main"
                      className="w-full h-full object-cover object-center"
                    />
                  </div>
                  {targetProduct.subImages.map((img, i) => (
                    <div
                      key={i}
                      onClick={() => setCurrentImage(img)}
                      className={`border rounded-lg cursor-pointer overflow-hidden ${
                        currentImage === img
                          ? "ring-2 ring-black"
                          : "hover:border-gray-400"
                      }`}
                    >
                      <img
                        src={img}
                        alt={`View ${i + 1}`}
                        className="w-full h-full object-cover object-center"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Mobile View - Carousel */}
              <div className="md:hidden">
                <Carousel className="w-full">
                  <CarouselContent>
                    <CarouselItem>
                      <div className="relative h-[350px] bg-gray-50 rounded-xl overflow-hidden">
                        <img
                          src={targetProduct.image}
                          alt={targetProduct.name}
                          className="w-full h-full object-contain p-4"
                        />
                        {/* Product Badges */}
                        <div className="absolute top-4 left-4 flex flex-col gap-2">
                          {targetProduct.price > 80 && (
                            <Badge className="bg-black text-white px-3 py-1 text-sm font-medium rounded-full">
                              PREMIUM
                            </Badge>
                          )}
                          <Badge className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                            NEW
                          </Badge>
                        </div>
                      </div>
                    </CarouselItem>
                    {targetProduct.subImages.map((img, i) => (
                      <CarouselItem key={i}>
                        <div className="relative h-[350px] bg-gray-50 rounded-xl overflow-hidden">
                          <img
                            src={img}
                            alt={`${targetProduct.name} view ${i + 1}`}
                            className="w-full h-full object-contain p-4"
                          />
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  <CarouselPrevious className="left-2" />
                  <CarouselNext className="right-2" />
                </Carousel>

                {/* Mobile Thumbnails */}
                <div className="grid grid-cols-5 gap-2 mt-4 h-16">
                  <div
                    onClick={() => setCurrentImage(targetProduct.image)}
                    className={`border rounded-lg cursor-pointer overflow-hidden ${
                      currentImage === targetProduct.image
                        ? "ring-2 ring-black"
                        : "hover:border-gray-400"
                    }`}
                  >
                    <img
                      src={targetProduct.image}
                      alt="Main"
                      className="w-full h-full object-cover object-center"
                    />
                  </div>
                  {targetProduct.subImages.map((img, i) => (
                    <div
                      key={i}
                      onClick={() => setCurrentImage(img)}
                      className={`border rounded-lg cursor-pointer overflow-hidden ${
                        currentImage === img
                          ? "ring-2 ring-black"
                          : "hover:border-gray-400"
                      }`}
                    >
                      <img
                        src={img}
                        alt={`View ${i + 1}`}
                        className="w-full h-full object-cover object-center"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Sharing */}
              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Share:</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-blue-600"
                    >
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-blue-800"
                    >
                      <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-pink-600"
                    >
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                  </Button>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={async () => {
                    try {
                      const result = await addToWishlist({
                        productId: targetProduct.id,
                        name: targetProduct.name,
                        price: targetProduct.price,
                        image: targetProduct.image,
                      });

                      if (result.success) {
                        // Add to client-side wishlist
                        addToWishlist({
                          id: result.product.id,
                          productId: result.product.productId,
                          name: result.product.name,
                          price: result.product.price,
                          image: result.product.image,
                          dateAdded: result.product.dateAdded,
                        });

                        toast({
                          title: "Added to wishlist",
                          description: `${targetProduct.name} has been added to your wishlist`,
                        });
                      } else {
                        toast({
                          title: "Error",
                          description: result.message,
                          variant: "destructive",
                        });
                      }
                    } catch (error) {
                      toast({
                        title: "Error",
                        description: "Failed to add product to wishlist",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  <Heart className="h-4 w-4" />
                  <span>Save</span>
                </Button>
              </div>
            </div>

            {/* Product Details - Right Column */}
            <div className="lg:w-1/2 p-6">
              <div className="max-w-xl">
                {/* Product Title and Rating */}
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="bg-green-500 text-white px-2 py-0.5 text-xs rounded-sm">
                      IN STOCK
                    </Badge>
                    <span className="text-sm text-gray-500">
                      SKU: {targetProduct.id.toString().padStart(6, "0")}
                    </span>
                  </div>

                  <h1 className="text-3xl font-bold mb-3">
                    {targetProduct.name}
                  </h1>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400" />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 ml-2">
                        4.8 (23 Reviews)
                      </span>
                    </div>

                    <div className="flex items-center text-gray-600 text-sm">
                      <Eye className="w-4 h-4 mr-1" />
                      <span>{viewersCount} viewing now</span>
                    </div>
                  </div>
                </div>

                {/* Price Section */}
                <div className="mb-6">
                  <div className="flex items-center mb-2">
                    <span className="text-3xl font-bold mr-3 text-gray-900">
                      ${targetProduct.price}.00
                    </span>
                    <span className="text-gray-500 line-through text-lg">
                      ${(targetProduct.price * 1.2).toFixed(2)}
                    </span>
                    <span className="ml-3 bg-red-100 text-red-700 px-2 py-1 text-xs font-medium rounded">
                      SAVE 20%
                    </span>
                  </div>

                  {/* Limited Time Offer */}
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2 text-amber-800 font-medium mb-2">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">Flash Sale Ends In:</span>
                    </div>
                    <div className="flex gap-2 text-center">
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[0]}
                        </div>
                        <div className="text-xs">Hours</div>
                      </div>
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[1]}
                        </div>
                        <div className="text-xs">Mins</div>
                      </div>
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[2]}
                        </div>
                        <div className="text-xs">Secs</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Medical Disclaimer */}
                <div className="mb-4">
                  <Alert className="bg-gray-50 border-gray-200">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs text-gray-600">
                      This product is not intended to diagnose, treat, cure, or
                      prevent any disease. Results may vary. Consult with a
                      healthcare professional before use if you have any medical
                      conditions.
                    </AlertDescription>
                  </Alert>
                </div>

                {/* Certification Badges */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Dermatologically Tested</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Paraben Free</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Cruelty Free</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Natural Ingredients</span>
                  </Badge>
                </div>

                {/* Short Description */}
                <div className="mb-6">
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {targetProduct.description.substring(0, 150)}...
                  </p>
                </div>

                {/* Size Selector */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Size
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-4 py-2 border rounded-md text-sm ${
                            selectedSize === size
                              ? "border-black bg-black text-white"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selector */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Color
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-4 py-2 border rounded-md text-sm ${
                            selectedColor === color
                              ? "border-black bg-black text-white"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">
                    Quantity
                  </h3>
                  <div className="flex">
                    <button
                      className="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-md hover:bg-gray-100"
                      onClick={() =>
                        cartCount > 1 && setCartCount(cartCount - 1)
                      }
                    >
                      -
                    </button>
                    <input
                      type="text"
                      value={cartCount}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value > 0) {
                          setCartCount(value);
                        }
                      }}
                      className="w-16 h-10 text-center border-t border-b border-gray-300"
                    />
                    <button
                      className="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-md hover:bg-gray-100"
                      onClick={() => setCartCount(cartCount + 1)}
                    >
                      +
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 mb-6">
                  <div className="flex-1">
                    <AddToCartButton
                      product={targetProduct}
                      quantity={cartCount}
                      selectedSize={selectedSize}
                      selectedColor={selectedColor}
                    />
                  </div>
                  <Button
                    className="w-full py-6 bg-amber-600 hover:bg-amber-700 text-white flex items-center justify-center gap-2 rounded-md"
                    onClick={() => {
                      // First add to cart, then redirect to checkout
                      const addToCartBtn =
                        document.querySelector("[data-add-to-cart]");
                      if (addToCartBtn) {
                        addToCartBtn.click();
                        setTimeout(() => {
                          window.location.href = "/checkout";
                        }, 500);
                      }
                    }}
                  >
                    <Zap className="w-5 h-5" />
                    Buy Now
                  </Button>
                </div>

                {/* Shipping Info */}
                <div className="border border-gray-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center text-gray-700 mb-3">
                    <Truck className="w-5 h-5 mr-2 text-green-600" />
                    <span className="font-medium">Free shipping</span>
                    <span className="text-sm ml-1">on orders over $100</span>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      30-day returns
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Secure checkout
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Authentic products
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Worldwide shipping
                    </div>
                  </div>
                </div>

                {/* Health Information */}
                <div className="border border-gray-200 rounded-lg p-4 mb-6 bg-blue-50/30">
                  <div className="flex items-center text-blue-700 mb-3">
                    <HeartPulse className="w-5 h-5 mr-2" />
                    <span className="font-medium">Health Information</span>
                  </div>
                  <div className="text-sm text-gray-700 space-y-2">
                    <p>
                      Always read the label before use. Not suitable for
                      children under 12.
                    </p>
                    <p>
                      Consult with a healthcare professional if symptoms
                      persist.
                    </p>
                  </div>
                </div>

                {/* Pharmacist Consultation */}
                <div className="mb-6">
                  <Button
                    variant="outline"
                    className="w-full py-3 border-blue-200 text-blue-700 hover:bg-blue-50 flex items-center justify-center gap-2"
                  >
                    <UserCog className="w-5 h-5" />
                    <span>Consult with a Pharmacist</span>
                  </Button>
                </div>

                {/* Ask a Question */}
                <div className="flex items-center justify-between">
                  <Button
                    variant="link"
                    className="text-blue-600 p-0 h-auto flex items-center gap-1"
                  >
                    <HelpCircle className="w-4 h-4" />
                    <span>Ask a Question</span>
                  </Button>
                  <Button
                    variant="link"
                    className="text-blue-600 p-0 h-auto flex items-center gap-1"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span>Chat with Us</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Tabs Section */}
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="bg-white rounded-xl shadow-sm overflow-hidden p-6 mb-8">
            <Tabs className="w-full flex flex-col gap-2">
              <TabList
                aria-label="Product Information"
                className="flex border-b"
              >
                <Tab
                  id="description"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Description
                </Tab>
                <Tab
                  id="ingredients"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Ingredients
                </Tab>
                <Tab
                  id="usage"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Usage & Dosage
                </Tab>
                <Tab
                  id="specifications"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Specifications
                </Tab>
                <Tab
                  id="shipping"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Shipping & Returns
                </Tab>
                <Tab
                  id="reviews"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Reviews (43)
                </Tab>
              </TabList>

              <TabPanel id="description" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Product Description
                  </h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed mb-4">
                      {targetProduct.description}
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      Our products are crafted with premium materials to ensure
                      durability and comfort. Each item undergoes rigorous
                      quality testing before reaching our customers.
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      The {targetProduct.name} is designed to provide
                      exceptional performance and value. Whether you're using it
                      at home, at work, or on the go, this product will exceed
                      your expectations with its reliability and effectiveness.
                    </p>
                  </div>

                  <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">Premium Quality</h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Made with the highest quality materials for durability
                        and performance.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">Eco-Friendly</h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Sustainably sourced and produced with minimal
                        environmental impact.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">
                          Satisfaction Guaranteed
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Try risk-free with our 30-day money-back guarantee
                        policy.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="ingredients" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">Ingredients</h2>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <Leaf className="w-5 h-5 mr-2 text-green-600" />
                      Active Ingredients
                    </h3>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                      <li>Aloe Vera (20%) - Soothing and cooling properties</li>
                      <li>Menthol (5%) - Provides cooling sensation</li>
                      <li>Eucalyptus Oil (2%) - Natural anti-inflammatory</li>
                      <li>
                        Arnica Extract (1%) - Helps reduce bruising and swelling
                      </li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3">
                      Other Ingredients
                    </h3>
                    <p className="text-gray-700 mb-3">
                      Purified Water, Glycerin, Cetyl Alcohol, Stearic Acid,
                      Dimethicone, Glyceryl Stearate, PEG-100 Stearate,
                      Carbomer, Triethanolamine, Phenoxyethanol,
                      Ethylhexylglycerin.
                    </p>
                  </div>

                  <Alert className="bg-amber-50 border-amber-200 mb-6">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <AlertTitle className="text-amber-800">
                      Allergen Information
                    </AlertTitle>
                    <AlertDescription className="text-amber-700">
                      This product contains eucalyptus oil which may cause
                      allergic reactions in some individuals. If you have known
                      allergies to botanical ingredients, please perform a patch
                      test before use.
                    </AlertDescription>
                  </Alert>

                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">Paraben-free</span>

                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">Cruelty-free</span>

                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">
                      Dermatologically tested
                    </span>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="usage" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">Usage & Dosage</h2>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <Pill className="w-5 h-5 mr-2 text-blue-600" />
                      Recommended Usage
                    </h3>
                    <div className="prose max-w-none text-gray-700">
                      <p className="mb-3">
                        Apply a thin layer of Forever Aloe Cooling Lotion to
                        affected areas up to 3-4 times daily, or as needed for
                        relief. Gently massage into skin until absorbed.
                      </p>
                      <p>
                        For best results, apply after physical activity or
                        whenever cooling relief is desired. Allow to dry
                        completely before covering with clothing.
                      </p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <HeartPulse className="w-5 h-5 mr-2 text-blue-600" />
                      Health Benefits
                    </h3>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                      <li>
                        Provides cooling relief to sore muscles and joints
                      </li>
                      <li>Helps reduce inflammation and discomfort</li>
                      <li>Moisturizes and soothes dry, irritated skin</li>
                      <li>Promotes faster recovery after physical exertion</li>
                      <li>Improves circulation in applied areas</li>
                    </ul>
                  </div>

                  <Alert className="bg-red-50 border-red-200 mb-6">
                    <ShieldAlert className="h-4 w-4 text-red-600" />
                    <AlertTitle className="text-red-800">
                      Warnings & Contraindications
                    </AlertTitle>
                    <AlertDescription className="text-red-700">
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>For external use only. Avoid contact with eyes.</li>
                        <li>Do not apply to broken or irritated skin.</li>
                        <li>Not recommended for children under 12 years.</li>
                        <li>Discontinue use if irritation occurs.</li>
                        <li>
                          Consult a healthcare professional before use if
                          pregnant or nursing.
                        </li>
                        <li>
                          Do not use with heating pads or immediately
                          before/after hot showers.
                        </li>
                      </ul>
                    </AlertDescription>
                  </Alert>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Calendar className="w-5 h-5 text-gray-600 mr-2" />
                        <h3 className="font-semibold">
                          Expiration Information
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        This product has a shelf life of 24 months from the
                        manufacturing date when unopened. Once opened, use
                        within 12 months for optimal effectiveness. Expiration
                        date is printed on the bottom of the container.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Thermometer className="w-5 h-5 text-gray-600 mr-2" />
                        <h3 className="font-semibold">
                          Storage Recommendations
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Store in a cool, dry place away from direct sunlight.
                        Keep at temperatures between 5°C and 25°C (41°F and
                        77°F). For an enhanced cooling effect, refrigeration is
                        recommended but not required.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="specifications" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Product Specifications
                  </h2>

                  <div className="overflow-hidden border border-gray-200 rounded-lg mb-6">
                    <table className="min-w-full divide-y divide-gray-200">
                      <tbody className="divide-y divide-gray-200">
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 w-1/3">
                            Brand
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Forever
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Model
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {targetProduct.name}
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Weight
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            250g
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Dimensions
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            10 × 5 × 3 cm
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Material
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Premium Aloe Vera
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Country of Origin
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            USA
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Warranty
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            1 Year Limited Warranty
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-blue-600"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">
                          Note about specifications
                        </h3>
                        <div className="mt-1 text-sm text-blue-700">
                          <p>
                            Specifications may vary slightly from the listed
                            information. Please refer to the product packaging
                            for the most accurate details.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="shipping" className="py-6">
                <div className=" mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Shipping & Returns
                  </h2>

                  <div className="mb-8">
                    <h3 className="text-lg font-semibold mb-3">
                      Shipping Information
                    </h3>
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-green-600 mr-3" />
                          <div>
                            <h4 className="font-medium">Standard Shipping</h4>
                            <p className="text-sm text-gray-600">
                              3-5 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">Free</div>
                        </div>
                      </div>
                      <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-amber-600 mr-3" />
                          <div>
                            <h4 className="font-medium">Express Shipping</h4>
                            <p className="text-sm text-gray-600">
                              1-2 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">$9.99</div>
                        </div>
                      </div>
                      <div className="px-6 py-4">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-blue-600 mr-3" />
                          <div>
                            <h4 className="font-medium">
                              International Shipping
                            </h4>
                            <p className="text-sm text-gray-600">
                              7-10 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">$19.99</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Return Policy
                    </h3>
                    <div className="prose max-w-none text-gray-700">
                      <p>
                        We want you to be completely satisfied with your
                        purchase. If for any reason you're not happy with your
                        order, we accept returns within 30 days of delivery.
                      </p>
                      <h4 className="text-base font-medium mt-4 mb-2">
                        To be eligible for a return, your item must be:
                      </h4>
                      <ul className="list-disc pl-5 space-y-1">
                        <li>In the same condition that you received it</li>
                        <li>Unworn or unused with tags still attached</li>
                        <li>In the original packaging</li>
                      </ul>
                      <h4 className="text-base font-medium mt-4 mb-2">
                        Return Process:
                      </h4>
                      <ol className="list-decimal pl-5 space-y-1">
                        <li>
                          Contact our customer service team to initiate a return
                        </li>
                        <li>
                          Pack the item securely in its original packaging
                        </li>
                        <li>Include your order number and return reason</li>
                        <li>
                          Ship the package to the address provided by customer
                          service
                        </li>
                      </ol>
                      <p className="mt-4">
                        Once we receive and inspect your return, we'll process
                        your refund. The money will be refunded to your original
                        payment method within 5-7 business days.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="reviews" className="py-6">
                <div className="mx-auto">
                  <div className="flex flex-col md:flex-row gap-8 mb-8">
                    <div className="md:w-1/3">
                      <div className="bg-gray-50 p-6 rounded-lg">
                        <div className="text-center mb-4">
                          <div className="text-5xl font-bold text-gray-900">
                            4.8
                          </div>
                          <div className="flex justify-center mt-2">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className="w-5 h-5 fill-yellow-400 text-yellow-400"
                              />
                            ))}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            Based on 43 reviews
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-sm w-8">5★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "85%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">85%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">4★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "10%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">10%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">3★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "5%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">5%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">2★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "0%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">0%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">1★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "0%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">0%</span>
                          </div>
                        </div>

                        <div className="mt-6">
                          <Button className="w-full bg-black hover:bg-gray-800 text-white">
                            Write a Review
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="md:w-2/3">
                      <div className="space-y-6">
                        <ReviewItem
                          name="John Smith"
                          date="March 15, 2023"
                          rating={5}
                          verified={true}
                          content="This product exceeded my expectations! The quality is outstanding and it looks even better in person. Shipping was fast and the packaging was excellent. Highly recommend!"
                          image="https://randomuser.me/api/portraits/men/32.jpg"
                        />
                        <ReviewItem
                          name="Sarah Johnson"
                          date="February 28, 2023"
                          rating={5}
                          verified={true}
                          content="I've been using this product for a month now and I'm very impressed with the results. It's exactly as described and works perfectly for my needs. Customer service was also excellent when I had questions."
                          image="https://randomuser.me/api/portraits/women/44.jpg"
                        />
                        <ReviewItem
                          name="Michael Brown"
                          date="January 12, 2023"
                          rating={4}
                          verified={true}
                          content="Great product overall. The only reason I'm giving 4 stars instead of 5 is because the delivery took a bit longer than expected. Otherwise, the product itself is fantastic and works great."
                          image="https://randomuser.me/api/portraits/men/22.jpg"
                        />
                      </div>

                      <div className="mt-8 text-center">
                        <Button
                          variant="outline"
                          className="border-gray-300 rounded-md"
                        >
                          Load More Reviews
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabPanel>
            </Tabs>
          </div>

          {/* Similar Products Section */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarProducts.map((item) => (
                <Link
                  key={item.id}
                  href={`/products/${item.slug}`}
                  className="bg-white rounded-xl shadow-sm overflow-hidden group hover:shadow-md transition-all duration-300"
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    />
                    {item.price > 90 && (
                      <Badge className="absolute top-2 left-2 bg-amber-500 text-white px-2 py-0.5 text-xs rounded-sm">
                        BESTSELLER
                      </Badge>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="flex text-yellow-400 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400" />
                      ))}
                    </div>
                    <h3 className="font-medium mb-1 text-gray-900">
                      {item.name}
                    </h3>
                    <div className="flex items-center">
                      <span className="font-bold text-gray-900">
                        ${item.price}.00
                      </span>
                      <span className="ml-2 text-sm text-gray-500 line-through">
                        ${(item.price * 1.2).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recently Viewed Section */}
          <div>
            <h2 className="text-2xl font-bold mb-6">Recently Viewed</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {product.slice(0, 4).map((item) => (
                <Link
                  key={item.id}
                  href={`/products/${item.slug}`}
                  className="bg-white rounded-xl shadow-sm overflow-hidden group hover:shadow-md transition-all duration-300"
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium mb-1 text-gray-900">
                      {item.name}
                    </h3>
                    <div className="flex items-center">
                      <span className="font-bold text-gray-900">
                        ${item.price}.00
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ReviewItemProps {
  name: string;
  date: string;
  rating: number;
  verified: boolean;
  content: string;
  image: string;
}

function ReviewItem({
  name,
  date,
  rating,
  verified,
  content,
  image,
}: ReviewItemProps) {
  return (
    <div className="border-b border-gray-200 pb-6">
      <div className="flex items-start">
        <div className="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden mr-4">
          <img src={image} alt={name} className="w-full h-full object-cover" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium text-gray-900">{name}</span>
              {verified && (
                <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">
                  Verified Purchase
                </span>
              )}
            </div>
            <span className="text-gray-500 text-sm">{date}</span>
          </div>
          <div className="flex gap-1 items-center mt-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < rating
                    ? "fill-yellow-400 text-yellow-400"
                    : "fill-gray-200 text-gray-200"
                }`}
              />
            ))}
          </div>
          <div className="mt-2">
            <p className="text-gray-700 text-sm">{content}</p>
          </div>
          <div className="mt-3 flex items-center gap-4">
            <button className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                />
              </svg>
              Helpful (3)
            </button>
            <button className="text-sm text-gray-500 hover:text-gray-700">
              Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
