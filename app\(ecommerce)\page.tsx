"use client";
import InView from "@/components/InView";
import Logo from "@/components/Logo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { product } from "@/data/product";
import {
  ArrowRight,
  Heart,
  ShoppingBag,
  Star,
  Shield,
  Truck,
  RotateCcw,
  Award,
  Users,
  Globe,
  Leaf,
  CheckCircle,
  Quote,
  Play,
  Instagram,
  Facebook,
  Twitter,
  TrendingUp,
  Package,
  Clock,
  Zap,
} from "lucide-react";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { motion, useAnimation, stagger, AnimatePresence } from "framer-motion";
import { useSearchParams } from "next/navigation";

// Container variant for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

// Item variant for children elements
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
    },
  },
};
const HeroVariantDefault = ({ controls }: { controls: any }) => (
  <main className="container relative mx-auto px-4 py-12 flex flex-col md:flex-row items-center min-h-[calc(100vh-72px)]">
    <div className="w-full md:w-1/2 md:pr-12 mb-12 md:mb-0 z-10">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={controls}
        className="space-y-8"
      >
        {/* Modern animated badge with gradient border */}
        <motion.div
          variants={itemVariants}
          className="relative"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <div
            style={{ width: "fit-content" }}
            className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-600 via-pink-500 to-indigo-600 blur-[1px] opacity-70 animate-pulse"
          ></div>
          <span className="relative inline-flex items-center gap-2 py-2 px-4 rounded-full text-xs font-medium bg-black/90 text-white backdrop-blur-sm border border-white/10 shadow-lg">
            <span className="animate-pulse font-semibold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              NEW
            </span>
            Spring Collection 2025{" "}
            <span className="relative flex h-2 w-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-500 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-purple-300"></span>
            </span>
          </span>
        </motion.div>
        {/* Modern headline with gradient and animated underline */}
        <div className="space-y-3">
          <motion.h1
            variants={itemVariants}
            className="text-5xl md:text-7xl font-bold tracking-tight leading-tight"
          >
            <span className="block mb-1 bg-gradient-to-br from-gray-900 via-purple-800 to-black bg-clip-text text-transparent">
              AirNags®
            </span>
            <div className="relative inline-block">
              <span className="block text-black/90 md:text-6xl">
                Premium Quality
              </span>
              <motion.span
                className="absolute -bottom-2 left-1 h-[6px] bg-gradient-to-r from-purple-600 via-pink-500 to-indigo-600 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "60%" }}
                transition={{
                  delay: 1.2,
                  duration: 0.8,
                  ease: "easeOut",
                }}
              ></motion.span>
            </div>
          </motion.h1>
        </div>
        {/* Enhanced description with better typography and animated highlight */}
        <motion.div variants={itemVariants} className="relative">
          <p className="text-lg md:text-xl text-gray-600 max-w-md leading-relaxed">
            Keep your everyday style{" "}
            <span className="relative inline-block">
              <span className="relative z-10">chic and on-trend</span>
              <span className="absolute bottom-0 left-0 w-full h-[30%] bg-purple-200/60 -rotate-1"></span>
            </span>{" "}
            with our carefully curated collection of{" "}
            <strong className="text-black">20+ premium styles</strong> designed
            for your comfort and expression.
          </p>
        </motion.div>
        {/* Modern buttons with micro-interactions */}{" "}
        <motion.div
          variants={itemVariants}
          className="flex flex-wrap gap-5 pt-4"
        >
          <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
            <Button className="group relative overflow-hidden rounded-full h-14 px-8 bg-black hover:bg-black/90 text-white shadow-lg hover:shadow-xl transition-all duration-300">
              <span className="relative z-10 flex items-center gap-2">
                Shop Collection
                <ShoppingBag className="h-4 w-4 transition-transform duration-500 group-hover:rotate-12" />
              </span>
              <span className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-lg"></span>
            </Button>
          </motion.div>

          <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
            <Button
              variant="outline"
              className="group rounded-full h-14 px-8 border-2 border-gray-300 bg-white/50 hover:border-purple-400 hover:bg-purple-50/50 transition-all duration-300 flex items-center gap-2"
            >
              <span>Explore More</span>
              <span className="relative w-4 overflow-hidden inline-flex transition-all duration-300">
                <ArrowRight className="h-4 w-4 absolute transform group-hover:translate-x-8 group-hover:opacity-0 transition-all duration-500" />
                <ArrowRight className="h-4 w-4 absolute -translate-x-8 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500" />
              </span>
            </Button>
          </motion.div>
        </motion.div>
        {/* Enhanced social proof section */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col md:flex-row items-start md:items-center gap-6 pt-6"
        >
          <div className="flex flex-col">
            <div className="flex items-center gap-2 mb-1.5">
              <div className="relative flex -space-x-3 z-10">
                {[1, 2, 3, 4].map((item) => (
                  <motion.div
                    key={item}
                    className="h-10 w-10 rounded-full border-2 border-white overflow-hidden shadow-md"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{
                      delay: 1.2 + item * 0.1,
                      duration: 0.4,
                    }}
                    whileHover={{ y: -3, scale: 1.05, zIndex: 20 }}
                  >
                    <img
                      src={`https://randomuser.me/api/portraits/${
                        item % 2 === 0 ? "women" : "men"
                      }/${item + 10}.jpg`}
                      alt="Customer"
                      className="h-full w-full object-cover"
                    />
                  </motion.div>
                ))}{" "}
                <motion.div
                  className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 border-2 border-white flex items-center justify-center text-xs font-medium text-white shadow-md"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 1.6, duration: 0.4 }}
                  whileHover={{ y: -3, scale: 1.05, zIndex: 20 }}
                >
                  +2k
                </motion.div>
              </div>
            </div>
            <span className="text-sm text-gray-500 font-medium pl-1">
              Joined our community
            </span>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center gap-1 mb-1.5">
              {Array(5)
                .fill(0)
                .map((_, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: 1.4 + idx * 0.1,
                      duration: 0.3,
                      type: "spring",
                    }}
                  >
                    <Star
                      className={`h-4 w-4 ${
                        idx < 4.5
                          ? "fill-amber-400 text-amber-400"
                          : "fill-gray-200 text-gray-200"
                      }`}
                    />
                  </motion.div>
                ))}
              <motion.span
                className="text-sm font-semibold ml-1.5 bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.9 }}
              >
                4.8/5
              </motion.span>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2 }}
              className="flex items-center gap-2"
            >
              <span className="text-sm text-gray-500 pl-1">
                From <strong className="text-gray-700">10k+</strong> happy
                customers
              </span>
              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                Verified
              </span>
            </motion.div>
          </div>
        </motion.div>
        {/* Decorative elements */}
        <motion.div
          className="absolute -left-12 -bottom-20 w-40 h-40 rounded-full bg-purple-300/20 blur-3xl z-0"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        ></motion.div>
      </motion.div>
    </div>

    <div className="w-full md:w-1/2 flex justify-center z-10">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut", delay: 0.5 }}
        className="relative"
      >
        {/* Enhanced gradient background with multiple layers */}
        <div
          className="absolute -inset-10 bg-gradient-to-br from-pink-600/30 via-purple-600/20 to-blue-500/30 opacity-60 blur-3xl rounded-full animate-pulse"
          style={{ animationDuration: "8s" }}
        ></div>
        <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-500 via-purple-600 to-indigo-500 opacity-25 blur-2xl rounded-full"></div>
        <div className="absolute top-10 right-10 w-40 h-40 bg-gradient-to-tl from-blue-400/40 to-emerald-400/40 opacity-40 blur-xl rounded-full"></div>

        {/* Main container for videos and floating elements */}
        <div className="relative w-full max-w-lg h-[400px]">
          {/* First video - main product showcase with enhanced border */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute top-0 left-10 w-72 h-auto rounded-2xl overflow-hidden shadow-xl"
            style={{ transform: "rotate(-5deg)" }}
            whileHover={{ scale: 1.02, rotate: "-3deg" }}
          >
            {/* Content remains the same */}
            <div className="relative pb-[177.77%] w-full">
              {/* Gradient border effect */}
              <div className="absolute inset-0 p-[2px] rounded-2xl z-10 bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 shadow-[0_0_15px_rgba(168,85,247,0.5)]">
                <div className="absolute inset-0 bg-black rounded-2xl">
                  <video
                    className="absolute inset-0 w-full h-full object-cover rounded-2xl mix-blend-normal"
                    autoPlay
                    loop
                    muted
                    playsInline
                  >
                    <source
                      src="https://media.useclip.com/campaigns/campaign-8010/clip_creator_9642_e8022668-7bb2-40b3-a738-bf13682953c8.mov"
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent p-4 rounded-b-2xl backdrop-blur-sm z-20">
              <h4 className="text-white text-sm font-medium">
                Premium Collection
              </h4>
              <div className="flex items-center justify-between">
                <p className="text-white/80 text-xs">Spring/Summer 2025</p>
                <div className="flex space-x-1">
                  <span className="w-1.5 h-1.5 bg-pink-500 rounded-full animate-pulse"></span>
                  <span
                    className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"
                    style={{ animationDelay: "0.5s" }}
                  ></span>
                  <span
                    className="w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse"
                    style={{ animationDelay: "1s" }}
                  ></span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Second video with updated animation */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.6 }}
            className="absolute bottom-0 right-0 w-56 h-auto rounded-2xl overflow-hidden shadow-xl"
            style={{ transform: "rotate(3deg)" }}
            whileHover={{ scale: 1.02, rotate: "5deg" }}
          >
            {/* Content remains the same */}
            <div className="relative pb-[177.77%] w-full">
              <div className="absolute inset-0 p-[2px] rounded-2xl z-10 bg-gradient-to-tr from-indigo-500 via-cyan-500 to-emerald-500 shadow-[0_0_15px_rgba(79,209,197,0.5)]">
                <div className="absolute inset-0 bg-black rounded-2xl">
                  <video
                    className="absolute inset-0 w-full h-full object-cover rounded-2xl mix-blend-normal"
                    autoPlay
                    loop
                    muted
                    playsInline
                  >
                    <source
                      src="https://media.useclip.com/campaigns/campaign-4502/clip_creator_15691_40d5716e-574d-49b4-bbbb-2f3439a32f42.mov"
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
            </div>
            <div className="absolute top-3 right-3 bg-gradient-to-r from-indigo-600/80 to-blue-600/80 backdrop-blur-md rounded-full px-3 py-1 shadow-lg z-20">
              <span className="text-white text-xs font-medium">
                Latest Design
              </span>
            </div>
          </motion.div>

          {/* Statistics Card with updated animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.2, duration: 0.5 }}
            className="absolute top-1/2 right-10 -translate-y-1/4 bg-white/90 backdrop-blur-md rounded-xl border border-white/20 shadow-[0_8px_30px_rgb(0,0,0,0.12)] p-4 max-w-[200px]"
            whileHover={{
              scale: 1.03,
              boxShadow: "0 10px 40px rgba(0,0,0,0.15)",
            }}
          >
            {/* Content remains the same */}
            <h4 className="text-sm font-medium mb-3 bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Performance Stats
            </h4>

            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-500">Comfort</span>
                <span className="text-xs font-medium">98%</span>
              </div>
              <div className="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                <div className="h-full w-[98%] bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
              </div>
            </div>

            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-500">Durability</span>
                <span className="text-xs font-medium">85%</span>
              </div>
              <div className="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                <div className="h-full w-[85%] bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full"></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-gray-500">Style</span>
                <span className="text-xs font-medium">92%</span>
              </div>
              <div className="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                <div className="h-full w-[92%] bg-gradient-to-r from-purple-400 to-pink-500 rounded-full"></div>
              </div>
            </div>
          </motion.div>

          {/* Available Sizes Card with updated animation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.4, duration: 0.5 }}
            className="absolute -left-4 bottom-20 bg-white/95 backdrop-blur-md rounded-xl border border-white/20 shadow-[0_8px_30px_rgb(0,0,0,0.12)] p-3 max-w-[130px]"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 40px rgba(0,0,0,0.15)",
            }}
          >
            {/* Content remains the same */}
            <div className="text-center">
              <span className="text-xs text-gray-500 block mb-2">
                Available Sizes
              </span>
              <div className="flex flex-wrap justify-center gap-1.5">
                {["XS", "S", "M", "L", "XL"].map((size) => (
                  <span
                    key={size}
                    className="inline-block px-2 py-1 text-xs font-medium bg-gradient-to-br from-gray-50 to-gray-100 hover:from-indigo-50 hover:to-purple-50 border border-gray-200 rounded-md transition-colors duration-200 cursor-pointer"
                  >
                    {size}
                  </span>
                ))}
              </div>
            </div>
          </motion.div>

          {/* New floating glow effects */}
          <div
            className="absolute top-1/3 left-1/3 w-20 h-20 bg-blue-500/20 rounded-full blur-xl animate-pulse"
            style={{ animationDuration: "6s" }}
          ></div>
          <div
            className="absolute bottom-1/4 left-1/4 w-16 h-16 bg-purple-500/20 rounded-full blur-xl animate-pulse"
            style={{ animationDuration: "7s" }}
          ></div>
          <div
            className="absolute top-1/4 right-1/4 w-24 h-24 bg-pink-500/20 rounded-full blur-xl animate-pulse"
            style={{ animationDuration: "5s" }}
          ></div>
        </div>
      </motion.div>
    </div>
  </main>
);
// Hero Variant 1 - Minimalist Clean Design
const HeroVariant1 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-white min-h-[calc(100vh-72px)]">
    {/* Clean geometric background */}
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="absolute top-20 right-20 w-72 h-72 bg-indigo-100 rounded-full opacity-60"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-purple-100 rounded-full opacity-40"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] border border-gray-200 rounded-full opacity-30"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-indigo-50 text-indigo-700 border-indigo-200 mb-6">
              ✨ New Collection 2025
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block text-gray-900">Premium</span>
            <span className="block bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Lifestyle
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg"
          >
            Discover our curated collection of premium products designed for the
            modern lifestyle. Quality meets style.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-black hover:bg-gray-800 text-white rounded-full px-8 py-4"
            >
              Shop Collection
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4"
            >
              Learn More
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
              alt="Premium Products"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
          </div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 2 - Bold Typography with Mesh Gradient
const HeroVariant2 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden min-h-[calc(100vh-72px)]">
    {/* Mesh Gradient Background */}
    <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(120,119,198,0.3),transparent_50%),radial-gradient(ellipse_at_top_right,rgba(255,105,180,0.3),transparent_50%),radial-gradient(ellipse_at_bottom_left,rgba(59,130,246,0.3),transparent_50%),radial-gradient(ellipse_at_bottom_right,rgba(168,85,247,0.3),transparent_50%)]"></div>
      <div className="absolute inset-0 opacity-40">
        <div className="w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMSIvPjwvZz48L2c+PC9zdmc+')]"></div>
      </div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col items-center justify-center min-h-[calc(100vh-72px)] text-center z-10">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={controls}
        className="space-y-8 max-w-4xl"
      >
        <motion.div variants={itemVariants}>
          <Badge className="bg-white/10 text-white border-white/20 backdrop-blur-sm mb-6">
            🚀 Revolutionary Design
          </Badge>
        </motion.div>

        <motion.h1
          variants={itemVariants}
          className="text-7xl lg:text-8xl font-black tracking-tight text-white leading-none"
        >
          FOREVER
          <span className="block bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
            COLLECTION
          </span>
        </motion.h1>

        <motion.p
          variants={itemVariants}
          className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed"
        >
          Experience the future of premium lifestyle products. Bold designs,
          exceptional quality, and innovative features that redefine your
          everyday.
        </motion.p>

        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-6 justify-center pt-8"
        >
          <Button
            size="lg"
            className="bg-white text-black hover:bg-gray-100 rounded-full px-10 py-4 text-lg font-semibold"
          >
            Explore Now
            <Zap className="ml-2 h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-white/30 text-white hover:bg-white/10 rounded-full px-10 py-4 text-lg"
          >
            Watch Demo
            <Play className="ml-2 h-5 w-5" />
          </Button>
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="pt-12 flex justify-center items-center gap-8 text-white/60"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-white">100K+</div>
            <div className="text-sm">Customers</div>
          </div>
          <div className="w-px h-12 bg-white/20"></div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white">4.9★</div>
            <div className="text-sm">Rating</div>
          </div>
          <div className="w-px h-12 bg-white/20"></div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white">50+</div>
            <div className="text-sm">Countries</div>
          </div>
        </motion.div>
      </motion.div>
    </main>
  </div>
);

// Hero Variant 3 - Split Screen with Video
const HeroVariant3 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-black min-h-[calc(100vh-72px)]">
    <div className="grid lg:grid-cols-2 min-h-[calc(100vh-72px)]">
      {/* Left Side - Content */}
      <div className="relative bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-8 lg:p-16">
        <div className="absolute inset-0 opacity-50">
          <div className="w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDMiPjxwYXRoIGQ9Ik0yMCAyMGMwLTExLjA0Ni04Ljk1NC0yMC0yMC0yMHYyMGgyMHoiLz48L2c+PC9zdmc+')]"></div>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-white z-10 max-w-lg"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-indigo-500/20 text-indigo-300 border-indigo-500/30 mb-6">
              💎 Luxury Edition
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-5xl lg:text-6xl font-bold leading-tight"
          >
            Redefine
            <span className="block bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              Excellence
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-lg text-gray-300 leading-relaxed"
          >
            Step into a world where luxury meets innovation. Our exclusive
            collection represents the pinnacle of design and craftsmanship.
          </motion.p>

          <motion.div variants={itemVariants} className="space-y-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-gray-300">Premium Materials</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-gray-300">Handcrafted Quality</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-gray-300">Limited Edition</span>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="pt-4">
            <Button
              size="lg"
              className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-full px-8 py-4"
            >
              Discover Collection
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Right Side - Video/Visual */}
      <div className="relative bg-gradient-to-bl from-indigo-600 to-purple-700 flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative z-10 max-w-md"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl backdrop-blur-sm"></div>
            <img
              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2126&q=80"
              alt="Luxury Product"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
          </div>

          {/* Floating Stats */}
          <div className="absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">4.9★</div>
              <div className="text-sm text-gray-600">Premium Rating</div>
            </div>
          </div>

          <div className="absolute -top-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">Limited</div>
              <div className="text-sm text-gray-600">Edition</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  </div>
);

// Hero Variant 4 - Original Design with Videos
const HeroVariant4 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-b from-white to-gray-50 min-h-[calc(100vh-72px)]">
    {/* Background Elements */}
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-indigo-200 to-purple-300 rounded-full opacity-20 animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-pink-200 to-indigo-300 rounded-full opacity-15 animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] border border-indigo-200 rounded-full opacity-10"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border-indigo-200 mb-6">
              ✨ New Collection 2025
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block text-gray-900">Forever</span>
            <span className="block bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Discover timeless elegance with our premium collection. Where luxury
            meets everyday comfort in perfect harmony.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full px-8 py-4"
            >
              Shop Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-gray-300"
            >
              <Play className="mr-2 h-4 w-4" />
              Watch Video
            </Button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex items-center gap-8 justify-center lg:justify-start pt-4"
          >
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="w-8 h-8 rounded-full border-2 border-white overflow-hidden"
                  >
                    <img
                      src={`https://randomuser.me/api/portraits/women/${
                        i + 10
                      }.jpg`}
                      alt="Customer"
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
              <div className="text-sm text-gray-600">
                <span className="font-semibold">10K+</span> happy customers
              </div>
            </div>
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="h-4 w-4 text-amber-400 fill-amber-400"
                />
              ))}
              <span className="text-sm text-gray-600 ml-1">4.9 rating</span>
            </div>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="grid grid-cols-2 gap-4">
            {/* Video 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="relative group"
            >
              <video
                autoPlay
                muted
                loop
                playsInline
                className="w-full h-48 object-cover rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
              >
                <source src="/hero-video-1.mp4" type="video/mp4" />
              </video>
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>
            </motion.div>

            {/* Video 2 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.6 }}
              className="relative group mt-8"
            >
              <video
                autoPlay
                muted
                loop
                playsInline
                className="w-full h-48 object-cover rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
              >
                <source src="/hero-video-2.mp4" type="video/mp4" />
              </video>
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>
            </motion.div>

            {/* Static Image */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
              className="relative group col-span-2"
            >
              <img
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                alt="Forever Collection"
                className="w-full h-32 object-cover rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>
            </motion.div>
          </div>

          {/* Floating Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.1, duration: 0.6 }}
            className="absolute -bottom-4 -right-4 bg-white rounded-2xl shadow-xl p-4 border border-gray-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">50%</div>
              <div className="text-xs text-gray-600">Off First Order</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 5 - Beauty & Wellness Gradient
const HeroVariant5 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden min-h-[calc(100vh-72px)]">
    {/* Beauty Gradient Background */}
    <div className="absolute inset-0 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(251,207,232,0.4),transparent_50%),radial-gradient(ellipse_at_top_right,rgba(196,181,253,0.3),transparent_50%),radial-gradient(ellipse_at_bottom_left,rgba(252,231,243,0.4),transparent_50%),radial-gradient(ellipse_at_bottom_right,rgba(219,234,254,0.3),transparent_50%)]"></div>

      {/* Floating Beauty Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-pink-200/30 to-rose-300/30 rounded-full blur-xl animate-pulse"></div>
      <div
        className="absolute bottom-32 right-32 w-40 h-40 bg-gradient-to-br from-purple-200/30 to-indigo-300/30 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: "2s" }}
      ></div>
      <div
        className="absolute top-1/2 left-1/3 w-24 h-24 bg-gradient-to-br from-blue-200/30 to-cyan-300/30 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: "1s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 border-pink-200 mb-6">
              ✨ Beauty & Wellness
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight leading-tight"
          >
            <span className="block bg-gradient-to-r from-pink-600 via-rose-500 to-purple-600 bg-clip-text text-transparent">
              Radiant
            </span>
            <span className="block text-gray-900">Beauty</span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Starts Here
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Discover premium parapharmacie and beauty products that enhance your
            natural glow. Professional-grade skincare for every skin type.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white rounded-full px-8 py-4"
            >
              Shop Skincare
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-pink-300 text-pink-700 hover:bg-pink-50"
            >
              Beauty Guide
            </Button>
          </motion.div>

          {/* Beauty Stats */}
          <motion.div
            variants={itemVariants}
            className="flex items-center gap-8 justify-center lg:justify-start pt-4"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">98%</div>
              <div className="text-sm text-gray-600">Satisfaction</div>
            </div>
            <div className="w-px h-8 bg-pink-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">500+</div>
              <div className="text-sm text-gray-600">Products</div>
            </div>
            <div className="w-px h-8 bg-pink-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-rose-600">24h</div>
              <div className="text-sm text-gray-600">Delivery</div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          {/* Main Beauty Product Image */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-300/20 to-purple-400/20 rounded-3xl blur-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-pink-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Floating Product Cards */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -left-6 top-1/4 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl max-w-[160px]"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Heart className="h-6 w-6 text-pink-600" />
              </div>
              <div className="text-sm font-semibold text-gray-900">
                Skincare
              </div>
              <div className="text-xs text-gray-600">Premium Range</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -right-6 bottom-1/4 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl max-w-[160px]"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-sm font-semibold text-gray-900">
                Fast Results
              </div>
              <div className="text-xs text-gray-600">Visible in 7 days</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 6 - Skincare Laboratory
const HeroVariant6 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-white min-h-[calc(100vh-72px)]">
    {/* Laboratory Grid Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-cyan-50"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBmMmZlIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-30"></div>

      {/* Floating Science Elements */}
      <div
        className="absolute top-32 right-20 w-20 h-20 bg-gradient-to-br from-cyan-200/40 to-blue-300/40 rounded-full animate-bounce"
        style={{ animationDuration: "3s" }}
      ></div>
      <div
        className="absolute bottom-40 left-32 w-16 h-16 bg-gradient-to-br from-teal-200/40 to-cyan-300/40 rounded-full animate-bounce"
        style={{ animationDuration: "4s", animationDelay: "1s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-cyan-100 text-cyan-700 border-cyan-200 mb-6">
              🧪 Science-Backed
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block text-gray-900">Clinical</span>
            <span className="block bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">
              Skincare
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Laboratory Tested
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Professional-grade formulations developed in our laboratories.
            Clinically proven ingredients for visible results.
          </motion.p>

          {/* Clinical Features */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <CheckCircle className="h-5 w-5 text-cyan-600" />
              <span className="text-gray-700">Dermatologist Tested</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <CheckCircle className="h-5 w-5 text-cyan-600" />
              <span className="text-gray-700">Hypoallergenic Formula</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <CheckCircle className="h-5 w-5 text-cyan-600" />
              <span className="text-gray-700">Clinical Studies Available</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 text-white rounded-full px-8 py-4"
            >
              View Clinical Range
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-cyan-300 text-cyan-700 hover:bg-cyan-50"
            >
              Research Papers
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          {/* Laboratory Equipment Visual */}
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1583947215259-38e31be8751f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Laboratory Skincare"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-cyan-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Scientific Data Cards */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-white rounded-2xl p-4 shadow-xl border border-cyan-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-cyan-600 mb-1">99.2%</div>
              <div className="text-sm text-gray-600">Efficacy Rate</div>
              <div className="text-xs text-gray-500 mt-1">Clinical Study</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-white rounded-2xl p-4 shadow-xl border border-blue-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                7 Days
              </div>
              <div className="text-sm text-gray-600">Visible Results</div>
              <div className="text-xs text-gray-500 mt-1">Average Time</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 7 - Natural & Organic
const HeroVariant7 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden min-h-[calc(100vh-72px)]">
    {/* Natural Background */}
    <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(34,197,94,0.1),transparent_50%),radial-gradient(ellipse_at_bottom_right,rgba(20,184,166,0.1),transparent_50%)]"></div>

      {/* Floating Organic Elements */}
      <div className="absolute top-20 right-20 w-40 h-40 bg-gradient-to-br from-green-200/20 to-emerald-300/20 rounded-full blur-2xl animate-pulse"></div>
      <div
        className="absolute bottom-20 left-20 w-32 h-32 bg-gradient-to-br from-teal-200/20 to-cyan-300/20 rounded-full blur-2xl animate-pulse"
        style={{ animationDelay: "1.5s" }}
      ></div>

      {/* Organic Shapes */}
      <div className="absolute top-1/3 left-1/4 w-20 h-20 bg-green-300/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-emerald-300/10 rounded-full blur-xl"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-green-100 text-green-700 border-green-200 mb-6">
              🌿 100% Natural
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              Pure
            </span>
            <span className="block text-gray-900">Nature</span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Organic Beauty
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Harness the power of nature with our certified organic skincare
            range. Pure, sustainable, and ethically sourced ingredients.
          </motion.p>

          {/* Organic Certifications */}
          <motion.div
            variants={itemVariants}
            className="flex flex-wrap gap-4 justify-center lg:justify-start"
          >
            <div className="flex items-center gap-2 bg-white/80 rounded-full px-4 py-2 shadow-sm">
              <Leaf className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">
                Organic Certified
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 rounded-full px-4 py-2 shadow-sm">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">
                Cruelty Free
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 rounded-full px-4 py-2 shadow-sm">
              <Globe className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">
                Sustainable
              </span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-full px-8 py-4"
            >
              Shop Organic
              <Leaf className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-green-300 text-green-700 hover:bg-green-50"
            >
              Learn About Ingredients
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          {/* Natural Products Image */}
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1556228720-195a672e8a03?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Natural Organic Products"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Organic Badges */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -top-6 -left-6 bg-white rounded-2xl p-4 shadow-xl border border-green-100"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Leaf className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-sm font-semibold text-gray-900">100%</div>
              <div className="text-xs text-gray-600">Organic</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -bottom-6 -right-6 bg-white rounded-2xl p-4 shadow-xl border border-emerald-100"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Globe className="h-6 w-6 text-emerald-600" />
              </div>
              <div className="text-sm font-semibold text-gray-900">Carbon</div>
              <div className="text-xs text-gray-600">Neutral</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 8 - Anti-Aging Luxury
const HeroVariant8 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 min-h-[calc(100vh-72px)]">
    {/* Luxury Golden Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(251,191,36,0.1),transparent_70%)]"></div>
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-amber-200/20 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-yellow-200/20 to-transparent rounded-full blur-3xl"></div>

      {/* Golden Particles */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-amber-400 rounded-full animate-ping"></div>
      <div
        className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-400 rounded-full animate-ping"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping"
        style={{ animationDelay: "2s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 border-amber-200 mb-6">
              ✨ Anti-Aging Excellence
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block text-gray-900">Timeless</span>
            <span className="block bg-gradient-to-r from-amber-600 via-yellow-500 to-orange-600 bg-clip-text text-transparent">
              Elegance
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Age Gracefully
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Revolutionary anti-aging formulations with gold peptides and rare
            botanicals. Turn back time with luxury skincare that delivers
            results.
          </motion.p>

          {/* Luxury Features */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">24K</span>
              </div>
              <span className="text-gray-700">24K Gold Infused</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <Award className="h-5 w-5 text-amber-600" />
              <span className="text-gray-700">Award-Winning Formula</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <Star className="h-5 w-5 text-amber-600 fill-amber-600" />
              <span className="text-gray-700">Celebrity Endorsed</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-amber-600 to-yellow-600 hover:from-amber-700 hover:to-yellow-700 text-white rounded-full px-8 py-4"
            >
              Discover Luxury
              <Star className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-amber-300 text-amber-700 hover:bg-amber-50"
            >
              Book Consultation
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          {/* Luxury Product Image */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-300/20 to-yellow-400/20 rounded-3xl blur-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1570194065650-d99fb4bedf0a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Luxury Anti-Aging Products"
              className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-amber-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Luxury Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-amber-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent mb-1">
                -15 Years
              </div>
              <div className="text-sm text-gray-600">Visible Age Reduction</div>
              <div className="text-xs text-gray-500 mt-1">Clinical Results</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-yellow-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent mb-1">
                $2M+
              </div>
              <div className="text-sm text-gray-600">Research Investment</div>
              <div className="text-xs text-gray-500 mt-1">Development Cost</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 9 - Wellness & Spa
const HeroVariant9 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-[calc(100vh-72px)]">
    {/* Spa Zen Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(99,102,241,0.05),transparent_70%)]"></div>

      {/* Floating Zen Elements */}
      <div
        className="absolute top-32 left-32 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-300/20 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "4s" }}
      ></div>
      <div
        className="absolute bottom-32 right-32 w-40 h-40 bg-gradient-to-br from-slate-200/20 to-blue-300/20 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "6s", animationDelay: "2s" }}
      ></div>

      {/* Zen Circles */}
      <div className="absolute top-1/4 right-1/4 w-16 h-16 border border-blue-200/30 rounded-full"></div>
      <div className="absolute bottom-1/4 left-1/4 w-20 h-20 border border-indigo-200/30 rounded-full"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border border-slate-200/20 rounded-full"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-blue-100 text-blue-700 border-blue-200 mb-6">
              🧘‍♀️ Wellness & Spa
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block text-gray-900">Inner</span>
            <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Harmony
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Wellness Journey
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Transform your daily routine into a spa experience. Professional
            wellness products for mind, body, and soul rejuvenation.
          </motion.p>

          {/* Wellness Benefits */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-xs text-blue-600">🧘</span>
              </div>
              <span className="text-gray-700">Stress Relief Formula</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
                <span className="text-xs text-indigo-600">💆</span>
              </div>
              <span className="text-gray-700">Spa-Grade Ingredients</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-slate-100 rounded-full flex items-center justify-center">
                <span className="text-xs text-slate-600">🌸</span>
              </div>
              <span className="text-gray-700">Aromatherapy Infused</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-full px-8 py-4"
            >
              Start Wellness Journey
              <Heart className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-blue-300 text-blue-700 hover:bg-blue-50"
            >
              Spa Consultation
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          {/* Spa Products Image */}
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Wellness Spa Products"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Wellness Stats */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -left-6 top-1/3 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-blue-100"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-lg">🧘‍♀️</span>
              </div>
              <div className="text-sm font-semibold text-gray-900">Mindful</div>
              <div className="text-xs text-gray-600">Wellness</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -right-6 bottom-1/3 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-indigo-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600 mb-1">95%</div>
              <div className="text-sm text-gray-600">Relaxation</div>
              <div className="text-xs text-gray-500 mt-1">Achieved</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 10 - Holographic Beauty Tech
const HeroVariant10 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-black min-h-[calc(100vh-72px)]">
    {/* Holographic Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-blue-900/20"></div>
      <div
        className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(147,51,234,0.1),rgba(236,72,153,0.1),rgba(59,130,246,0.1),rgba(147,51,234,0.1))] animate-spin"
        style={{ animationDuration: "20s" }}
      ></div>

      {/* Holographic Grid */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDI1NSwyNTUsMjU1LDAuMDUpIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-30"></div>

      {/* Floating Holographic Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-pulse"></div>
      <div
        className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute top-1/2 left-1/3 w-24 h-24 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: "2s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border-purple-400/30 backdrop-blur-sm mb-6">
              🚀 Future Beauty Tech
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-black tracking-tight leading-none"
          >
            <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent animate-pulse">
              NEXT-GEN
            </span>
            <span className="block text-white mt-2">BEAUTY</span>
            <span className="block text-2xl lg:text-3xl font-normal bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mt-4">
              Holographic Skincare
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-lg leading-relaxed"
          >
            Experience the future of beauty with our revolutionary
            holographic-infused formulations. Advanced nanotechnology meets
            luxury skincare.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white rounded-full px-8 py-4 shadow-lg shadow-purple-500/25"
            >
              Enter the Future
              <Zap className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-purple-400/30 text-purple-300 hover:bg-purple-500/10 backdrop-blur-sm"
            >
              Tech Specs
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 via-pink-500/30 to-blue-500/30 rounded-3xl blur-2xl animate-pulse"></div>
            <img
              src="https://images.unsplash.com/photo-1596755389378-c31d21fd1273?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Future Beauty Tech"
              className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/40 via-transparent to-pink-900/20 rounded-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-purple-500/10 to-transparent rounded-3xl animate-pulse"></div>
          </div>

          {/* Floating Tech Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-black/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-purple-500/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-1">
                99.9%
              </div>
              <div className="text-sm text-gray-300">Absorption Rate</div>
              <div className="text-xs text-gray-500 mt-1">Nano-Tech</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-black/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-blue-500/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-1">
                2050
              </div>
              <div className="text-sm text-gray-300">Future Formula</div>
              <div className="text-xs text-gray-500 mt-1">Advanced</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 11 - Minimalist Luxury Glass
const HeroVariant11 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white min-h-[calc(100vh-72px)]">
    {/* Glass Morphism Background */}
    <div className="absolute inset-0">
      <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-rose-200/30 to-pink-300/30 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-purple-200/20 to-indigo-300/20 rounded-full blur-3xl"></div>

      {/* Glass Elements */}
      <div className="absolute top-1/3 left-1/4 w-32 h-32 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg rotate-12"></div>
      <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg -rotate-12"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-white/40 backdrop-blur-sm border border-white/30 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-rose-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">
                Luxury Minimalism
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-7xl lg:text-8xl font-thin tracking-tight leading-none"
          >
            <span className="block text-gray-900 font-light">Pure</span>
            <span className="block text-gray-900 font-extralight italic">
              Elegance
            </span>
            <span className="block text-lg lg:text-xl font-normal text-gray-500 mt-6 tracking-wide">
              MINIMALIST BEAUTY COLLECTION
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed font-light"
          >
            Less is more. Discover our curated selection of essential beauty
            products with clean formulations and timeless elegance.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4"
          >
            <Button
              size="lg"
              className="bg-gray-900 hover:bg-gray-800 text-white rounded-none px-12 py-4 font-light tracking-wide"
            >
              EXPLORE COLLECTION
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-none px-12 py-4 border-gray-300 text-gray-700 hover:bg-gray-50 font-light tracking-wide"
            >
              PHILOSOPHY
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-3xl border border-white/30 shadow-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Minimalist Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-white/20 to-transparent rounded-3xl"></div>
          </div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 12 - Gradient Liquid Beauty
const HeroVariant12 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden min-h-[calc(100vh-72px)]">
    {/* Liquid Gradient Background */}
    <div className="absolute inset-0 bg-gradient-to-br from-violet-500 via-purple-500 to-fuchsia-500">
      <div className="absolute inset-0 bg-gradient-to-tl from-pink-400/30 via-transparent to-blue-400/30"></div>

      {/* Animated Liquid Blobs */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
        <div
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-pink-400/40 to-rose-500/40 rounded-full blur-3xl animate-pulse"
          style={{ animationDuration: "8s" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-blue-400/40 to-indigo-500/40 rounded-full blur-3xl animate-pulse"
          style={{ animationDuration: "6s", animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"
          style={{ animationDuration: "10s", animationDelay: "1s" }}
        ></div>
      </div>

      {/* Floating Particles */}
      <div
        className="absolute top-1/4 left-1/4 w-4 h-4 bg-white/60 rounded-full animate-bounce"
        style={{ animationDelay: "0s" }}
      ></div>
      <div
        className="absolute top-1/3 right-1/3 w-3 h-3 bg-white/40 rounded-full animate-bounce"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-white/50 rounded-full animate-bounce"
        style={{ animationDelay: "2s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-white/20 text-white border-white/30 backdrop-blur-sm mb-6">
              💧 Liquid Beauty
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
          >
            <span className="block text-white drop-shadow-lg">Liquid</span>
            <span className="block bg-gradient-to-r from-white via-pink-200 to-blue-200 bg-clip-text text-transparent drop-shadow-lg">
              Perfection
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-white/90 mt-4 drop-shadow">
              Fluid Beauty Innovation
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 max-w-lg leading-relaxed drop-shadow"
          >
            Immerse yourself in our revolutionary liquid-based beauty
            formulations. Seamless application, flawless results, infinite
            possibilities.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border border-white/30 rounded-full px-8 py-4 shadow-lg"
            >
              Discover Liquid Beauty
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-white/40 text-white hover:bg-white/10 backdrop-blur-sm"
            >
              Innovation Lab
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 shadow-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Liquid Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/30 to-transparent rounded-3xl"></div>
          </div>

          {/* Floating Liquid Drops */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-white/20 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">💧</div>
              <div className="text-sm text-white/90">Liquid Formula</div>
              <div className="text-xs text-white/70 mt-1">Seamless Blend</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-white/20 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">∞</div>
              <div className="text-sm text-white/90">Infinite Shades</div>
              <div className="text-xs text-white/70 mt-1">Custom Blend</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 13 - Botanical Garden
const HeroVariant13 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-emerald-50 via-green-50 to-lime-50 min-h-[calc(100vh-72px)]">
    {/* Botanical Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(34,197,94,0.1),transparent_50%),radial-gradient(ellipse_at_bottom_right,rgba(132,204,22,0.1),transparent_50%)]"></div>

      {/* Floating Botanical Elements */}
      <div
        className="absolute top-20 right-20 w-40 h-40 bg-gradient-to-br from-green-200/30 to-emerald-300/30 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "5s" }}
      ></div>
      <div
        className="absolute bottom-20 left-20 w-32 h-32 bg-gradient-to-br from-lime-200/30 to-green-300/30 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "7s", animationDelay: "2s" }}
      ></div>

      {/* Botanical Shapes */}
      <div className="absolute top-1/4 left-1/4 w-24 h-24 bg-green-300/20 rounded-full blur-xl"></div>
      <div className="absolute bottom-1/3 right-1/3 w-20 h-20 bg-emerald-300/20 rounded-full blur-xl"></div>
      <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-lime-300/20 rounded-full blur-xl"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-green-100 text-green-700 border-green-200 mb-6">
              🌱 Botanical Garden
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-7xl font-bold tracking-tight"
          >
            <span className="block bg-gradient-to-r from-green-700 to-emerald-600 bg-clip-text text-transparent">
              Garden
            </span>
            <span className="block text-gray-900">Fresh</span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-2">
              Botanical Beauty
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Cultivated from nature's finest botanicals. Our garden-fresh
            formulations bring the power of plants directly to your skincare
            routine.
          </motion.p>

          {/* Botanical Features */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 gap-4"
          >
            <div className="flex items-center gap-3 bg-white/60 rounded-2xl p-4 shadow-sm">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-lg">🌿</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  Plant-Based
                </div>
                <div className="text-xs text-gray-600">100% Natural</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-white/60 rounded-2xl p-4 shadow-sm">
              <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                <span className="text-lg">🌸</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  Fresh Extract
                </div>
                <div className="text-xs text-gray-600">Daily Harvest</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-white/60 rounded-2xl p-4 shadow-sm">
              <div className="w-8 h-8 bg-lime-100 rounded-full flex items-center justify-center">
                <span className="text-lg">🍃</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  Organic
                </div>
                <div className="text-xs text-gray-600">Certified</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-white/60 rounded-2xl p-4 shadow-sm">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-lg">🌺</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  Botanical
                </div>
                <div className="text-xs text-gray-600">Essence</div>
              </div>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-full px-8 py-4"
            >
              Explore Garden
              <Leaf className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-green-300 text-green-700 hover:bg-green-50"
            >
              Botanical Guide
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1616401784845-180882ba9ba8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Botanical Beauty Garden"
              className="w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Floating Garden Elements */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -top-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-green-100"
          >
            <div className="text-center">
              <div className="text-2xl mb-1">🌱</div>
              <div className="text-sm font-semibold text-gray-900">Fresh</div>
              <div className="text-xs text-gray-600">Daily</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -bottom-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-emerald-100"
          >
            <div className="text-center">
              <div className="text-2xl mb-1">🌿</div>
              <div className="text-sm font-semibold text-gray-900">Pure</div>
              <div className="text-xs text-gray-600">Botanical</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 14 - Cosmic Beauty
const HeroVariant14 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-[calc(100vh-72px)]">
    {/* Cosmic Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(139,69,19,0.1),transparent_70%)]"></div>

      {/* Stars */}
      <div className="absolute top-20 left-20 w-1 h-1 bg-white rounded-full animate-pulse"></div>
      <div
        className="absolute top-40 right-32 w-1 h-1 bg-white rounded-full animate-pulse"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute bottom-32 left-40 w-1 h-1 bg-white rounded-full animate-pulse"
        style={{ animationDelay: "2s" }}
      ></div>
      <div
        className="absolute top-60 left-1/3 w-1 h-1 bg-white rounded-full animate-pulse"
        style={{ animationDelay: "0.5s" }}
      ></div>
      <div
        className="absolute bottom-40 right-1/4 w-1 h-1 bg-white rounded-full animate-pulse"
        style={{ animationDelay: "1.5s" }}
      ></div>

      {/* Cosmic Nebula */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-purple-500/20 via-pink-500/10 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-blue-500/20 via-indigo-500/10 to-transparent rounded-full blur-3xl"></div>

      {/* Cosmic Particles */}
      <div
        className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "8s" }}
      ></div>
      <div
        className="absolute bottom-1/3 right-1/3 w-24 h-24 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl animate-pulse"
        style={{ animationDuration: "6s", animationDelay: "2s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <Badge className="bg-purple-500/20 text-purple-300 border-purple-400/30 backdrop-blur-sm mb-6">
              ✨ Cosmic Beauty
            </Badge>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
          >
            <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
              STELLAR
            </span>
            <span className="block text-white">GLOW</span>
            <span className="block text-2xl lg:text-3xl font-normal bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent mt-4">
              Cosmic Beauty Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-lg leading-relaxed"
          >
            Harness the power of the cosmos with our stellar beauty
            formulations. Infused with rare minerals and cosmic energy for an
            otherworldly glow.
          </motion.p>

          {/* Cosmic Features */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">✨</span>
              </div>
              <span className="text-gray-300">Stardust Infusion</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">🌟</span>
              </div>
              <span className="text-gray-300">Cosmic Minerals</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white">🌙</span>
              </div>
              <span className="text-gray-300">Lunar Essence</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white rounded-full px-8 py-4 shadow-lg shadow-purple-500/25"
            >
              Explore Cosmos
              <Star className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-purple-400/30 text-purple-300 hover:bg-purple-500/10 backdrop-blur-sm"
            >
              Stellar Guide
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-pink-500/20 to-blue-500/20 rounded-3xl blur-2xl animate-pulse"></div>
            <img
              src="https://images.unsplash.com/photo-1614732414444-096e5f1122d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Cosmic Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/40 via-transparent to-transparent rounded-3xl"></div>
          </div>

          {/* Floating Cosmic Elements */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-slate-900/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-purple-500/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-1">
                ∞
              </div>
              <div className="text-sm text-gray-300">Infinite Glow</div>
              <div className="text-xs text-gray-500 mt-1">Cosmic Power</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-slate-900/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-blue-500/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent mb-1">
                ✨
              </div>
              <div className="text-sm text-gray-300">Stellar Formula</div>
              <div className="text-xs text-gray-500 mt-1">Cosmic Energy</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 15 - Neon Glow Beauty
const HeroVariant15 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-black min-h-[calc(100vh-72px)]">
    {/* Neon Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/20 via-purple-900/20 to-pink-900/20"></div>

      {/* Neon Grid */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDAsMjU1LDI1NSwwLjEpIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-30"></div>

      {/* Neon Glow Effects */}
      <div className="absolute top-20 left-20 w-80 h-80 bg-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div
        className="absolute bottom-20 right-20 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-purple-500/10 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "2s" }}
      ></div>

      {/* Neon Lines */}
      <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"></div>
      <div className="absolute bottom-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-pink-400/50 to-transparent"></div>
      <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-purple-400/50 to-transparent"></div>
      <div className="absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-cyan-400/50 to-transparent"></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-cyan-500/10 backdrop-blur-sm border border-cyan-400/30 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-cyan-300">
                Neon Beauty Tech
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-black tracking-tight leading-none"
          >
            <span className="block text-white drop-shadow-[0_0_10px_rgba(0,255,255,0.5)]">
              NEON
            </span>
            <span className="block bg-gradient-to-r from-cyan-400 via-pink-400 to-purple-400 bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(255,0,255,0.5)]">
              GLOW
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-cyan-300 mt-4 drop-shadow-[0_0_10px_rgba(0,255,255,0.3)]">
              Cyberpunk Beauty Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-lg leading-relaxed"
          >
            Step into the future with our neon-infused beauty technology.
            Electrifying formulations that make you glow from within.
          </motion.p>

          {/* Neon Features */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center shadow-[0_0_10px_rgba(0,255,255,0.5)]">
                <Zap className="h-3 w-3 text-white" />
              </div>
              <span className="text-gray-300">Electric Glow Formula</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center shadow-[0_0_10px_rgba(255,0,255,0.5)]">
                <span className="text-xs text-white">⚡</span>
              </div>
              <span className="text-gray-300">Neon Pigments</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full flex items-center justify-center shadow-[0_0_10px_rgba(128,0,255,0.5)]">
                <span className="text-xs text-white">💫</span>
              </div>
              <span className="text-gray-300">Cyber Enhancement</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-cyan-500 via-pink-500 to-purple-500 hover:from-cyan-600 hover:via-pink-600 hover:to-purple-600 text-white rounded-full px-8 py-4 shadow-[0_0_20px_rgba(0,255,255,0.3)] hover:shadow-[0_0_30px_rgba(0,255,255,0.5)] transition-all duration-300"
            >
              Enter Neon World
              <Zap className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-cyan-400/30 text-cyan-300 hover:bg-cyan-500/10 backdrop-blur-sm hover:shadow-[0_0_20px_rgba(0,255,255,0.2)]"
            >
              Cyber Guide
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 via-pink-500/20 to-purple-500/20 rounded-3xl blur-2xl animate-pulse shadow-[0_0_50px_rgba(0,255,255,0.3)]"></div>
            <img
              src="https://images.unsplash.com/photo-1596755389378-c31d21fd1273?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Neon Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-pink-500/10 rounded-3xl"></div>
          </div>

          {/* Floating Neon Elements */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-black/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-cyan-500/30 shadow-[0_0_20px_rgba(0,255,255,0.2)]"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1">
                ⚡
              </div>
              <div className="text-sm text-cyan-300">Electric Glow</div>
              <div className="text-xs text-gray-500 mt-1">Neon Power</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-black/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-pink-500/30 shadow-[0_0_20px_rgba(255,0,255,0.2)]"
          >
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent mb-1">
                💫
              </div>
              <div className="text-sm text-pink-300">Cyber Beauty</div>
              <div className="text-xs text-gray-500 mt-1">Future Tech</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 16 - Crystal Luxury
const HeroVariant16 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-slate-100 via-gray-50 to-blue-50 min-h-[calc(100vh-72px)]">
    {/* Crystal Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(148,163,184,0.1),transparent_50%),radial-gradient(ellipse_at_bottom_right,rgba(59,130,246,0.1),transparent_50%)]"></div>

      {/* Crystal Refractions */}
      <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-blue-200/30 to-indigo-300/30 rounded-full blur-3xl transform rotate-45"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-slate-200/30 to-gray-300/30 rounded-full blur-3xl transform -rotate-45"></div>

      {/* Crystal Shapes */}
      <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30 shadow-lg transform rotate-12"></div>
      <div className="absolute bottom-1/3 right-1/3 w-24 h-24 bg-blue-100/30 backdrop-blur-sm rounded-lg border border-blue-200/30 shadow-lg transform -rotate-12"></div>
      <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-slate-100/30 backdrop-blur-sm rounded-lg border border-slate-200/30 shadow-lg transform rotate-45"></div>

      {/* Floating Crystal Particles */}
      <div className="absolute top-1/3 left-1/3 w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
      <div
        className="absolute bottom-1/4 right-1/4 w-1.5 h-1.5 bg-slate-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.5s" }}
      ></div>
      <div
        className="absolute top-2/3 left-1/2 w-1 h-1 bg-indigo-400 rounded-full animate-bounce"
        style={{ animationDelay: "1s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-white/40 backdrop-blur-sm border border-white/30 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-slate-700">
                💎 Crystal Luxury
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-thin tracking-tight leading-none"
          >
            <span className="block text-slate-900 font-extralight">
              CRYSTAL
            </span>
            <span className="block bg-gradient-to-r from-blue-600 via-indigo-600 to-slate-600 bg-clip-text text-transparent font-light">
              RADIANCE
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-slate-600 mt-4">
              Diamond Beauty Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-600 max-w-lg leading-relaxed font-light"
          >
            Experience the brilliance of crystal-infused beauty. Our
            diamond-grade formulations reflect light and enhance your natural
            luminosity.
          </motion.p>

          {/* Crystal Features */}
          <motion.div variants={itemVariants} className="space-y-4">
            <div className="flex items-center gap-4 bg-white/30 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center">
                <span className="text-2xl">💎</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-slate-900">
                  Diamond Particles
                </div>
                <div className="text-xs text-slate-600">
                  Micro-crystalline technology
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white/30 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-slate-100 to-gray-100 rounded-xl flex items-center justify-center">
                <span className="text-2xl">✨</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-slate-900">
                  Light Reflection
                </div>
                <div className="text-xs text-slate-600">
                  Prismatic glow enhancement
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white/30 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-xl flex items-center justify-center">
                <span className="text-2xl">🔮</span>
              </div>
              <div>
                <div className="text-sm font-semibold text-slate-900">
                  Crystal Clarity
                </div>
                <div className="text-xs text-slate-600">
                  Pure transparency formula
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-slate-800 to-blue-800 hover:from-slate-900 hover:to-blue-900 text-white rounded-none px-12 py-4 font-light tracking-wide shadow-lg"
            >
              DISCOVER CRYSTAL
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-none px-12 py-4 border-slate-300 text-slate-700 hover:bg-slate-50 font-light tracking-wide"
            >
              LUXURY GUIDE
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-3xl border border-white/30 shadow-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Crystal Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900/20 to-transparent rounded-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-indigo-500/10 rounded-3xl"></div>
          </div>

          {/* Floating Crystal Elements */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -top-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-blue-100"
          >
            <div className="text-center">
              <div className="text-2xl mb-1">💎</div>
              <div className="text-sm font-semibold text-slate-900">
                Premium
              </div>
              <div className="text-xs text-slate-600">Crystal</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -bottom-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-indigo-100"
          >
            <div className="text-center">
              <div className="text-2xl mb-1">✨</div>
              <div className="text-sm font-semibold text-slate-900">
                Radiant
              </div>
              <div className="text-xs text-slate-600">Glow</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 17 - Sunset Gradient Beauty
const HeroVariant17 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden min-h-[calc(100vh-72px)]">
    {/* Sunset Gradient Background */}
    <div className="absolute inset-0 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600">
      <div className="absolute inset-0 bg-gradient-to-t from-orange-300/30 via-transparent to-purple-400/30"></div>

      {/* Flowing Sunset Waves */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute -top-40 left-0 w-full h-96 bg-gradient-to-r from-yellow-400/40 via-orange-400/40 to-pink-400/40 rounded-full blur-3xl animate-pulse transform -rotate-12"
          style={{ animationDuration: "8s" }}
        ></div>
        <div
          className="absolute -bottom-40 right-0 w-full h-96 bg-gradient-to-r from-pink-400/40 via-purple-400/40 to-indigo-400/40 rounded-full blur-3xl animate-pulse transform rotate-12"
          style={{ animationDuration: "10s", animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-orange-300/20 via-pink-300/20 to-purple-300/20 rounded-full blur-3xl animate-pulse"
          style={{ animationDuration: "12s", animationDelay: "1s" }}
        ></div>
      </div>

      {/* Floating Sunset Particles */}
      <div
        className="absolute top-1/4 left-1/4 w-4 h-4 bg-yellow-300/80 rounded-full animate-bounce"
        style={{ animationDelay: "0s" }}
      ></div>
      <div
        className="absolute top-1/3 right-1/3 w-3 h-3 bg-orange-300/80 rounded-full animate-bounce"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-pink-300/80 rounded-full animate-bounce"
        style={{ animationDelay: "2s" }}
      ></div>
      <div
        className="absolute bottom-1/3 right-1/4 w-3 h-3 bg-purple-300/80 rounded-full animate-bounce"
        style={{ animationDelay: "1.5s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-white">
                🌅 Sunset Beauty
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
          >
            <span className="block text-white drop-shadow-lg">GOLDEN</span>
            <span className="block bg-gradient-to-r from-yellow-200 via-orange-200 to-pink-200 bg-clip-text text-transparent drop-shadow-lg">
              HOUR
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-white/90 mt-4 drop-shadow">
              Sunset Beauty Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 max-w-lg leading-relaxed drop-shadow"
          >
            Capture the magic of golden hour with our sunset-inspired beauty
            collection. Warm, radiant formulations that give you that perfect
            golden glow.
          </motion.p>

          {/* Sunset Features */}
          <motion.div variants={itemVariants} className="space-y-3">
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-xs text-white">🌅</span>
              </div>
              <span className="text-white/90">Golden Hour Glow</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-xs text-white">✨</span>
              </div>
              <span className="text-white/90">Warm Radiance</span>
            </div>
            <div className="flex items-center gap-3 justify-center lg:justify-start">
              <div className="w-6 h-6 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-xs text-white">🌸</span>
              </div>
              <span className="text-white/90">Sunset Pigments</span>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border border-white/30 rounded-full px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Embrace Golden Hour
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-white/40 text-white hover:bg-white/10 backdrop-blur-sm"
            >
              Sunset Guide
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 shadow-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="Sunset Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-orange-900/30 via-transparent to-transparent rounded-3xl"></div>
          </div>

          {/* Floating Sunset Elements */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="absolute -bottom-6 -left-6 bg-white/20 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">🌅</div>
              <div className="text-sm text-white/90">Golden Hour</div>
              <div className="text-xs text-white/70 mt-1">Perfect Glow</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="absolute -top-6 -right-6 bg-white/20 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">✨</div>
              <div className="text-sm text-white/90">Warm Radiance</div>
              <div className="text-xs text-white/70 mt-1">Natural Glow</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

// Hero Variant 18 - 3D Floating Elements
const HeroVariant18 = ({ controls }: { controls: any }) => (
  <div className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 min-h-[calc(100vh-72px)]">
    {/* 3D Background */}
    <div className="absolute inset-0">
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(99,102,241,0.05),transparent_70%)]"></div>

      {/* 3D Floating Cards */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-2xl shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-700 border border-indigo-100">
        <div className="p-4 h-full flex flex-col justify-center items-center">
          <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-lg">💄</span>
          </div>
          <div className="text-xs font-semibold text-gray-700">Lipstick</div>
        </div>
      </div>

      <div className="absolute top-40 right-32 w-28 h-28 bg-white rounded-2xl shadow-2xl transform -rotate-12 hover:rotate-0 transition-transform duration-700 border border-purple-100">
        <div className="p-4 h-full flex flex-col justify-center items-center">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-lg">🧴</span>
          </div>
          <div className="text-xs font-semibold text-gray-700">Serum</div>
        </div>
      </div>

      <div className="absolute bottom-32 left-32 w-36 h-36 bg-white rounded-2xl shadow-2xl transform rotate-6 hover:-rotate-3 transition-transform duration-700 border border-pink-100">
        <div className="p-4 h-full flex flex-col justify-center items-center">
          <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-xl">✨</span>
          </div>
          <div className="text-xs font-semibold text-gray-700">Foundation</div>
        </div>
      </div>

      <div className="absolute bottom-20 right-20 w-30 h-30 bg-white rounded-2xl shadow-2xl transform -rotate-6 hover:rotate-12 transition-transform duration-700 border border-blue-100">
        <div className="p-4 h-full flex flex-col justify-center items-center">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-lg">🎨</span>
          </div>
          <div className="text-xs font-semibold text-gray-700">Palette</div>
        </div>
      </div>

      {/* Floating 3D Spheres */}
      <div
        className="absolute top-1/4 left-1/3 w-16 h-16 bg-gradient-to-br from-indigo-200 to-purple-300 rounded-full shadow-xl animate-bounce"
        style={{ animationDuration: "3s" }}
      ></div>
      <div
        className="absolute bottom-1/3 right-1/3 w-12 h-12 bg-gradient-to-br from-pink-200 to-rose-300 rounded-full shadow-xl animate-bounce"
        style={{ animationDuration: "4s", animationDelay: "1s" }}
      ></div>
      <div
        className="absolute top-1/2 right-1/4 w-20 h-20 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full shadow-xl animate-bounce"
        style={{ animationDuration: "5s", animationDelay: "2s" }}
      ></div>
    </div>

    <main className="container relative mx-auto px-4 py-20 flex flex-col lg:flex-row items-center min-h-[calc(100vh-72px)]">
      <div className="w-full lg:w-1/2 lg:pr-16 mb-12 lg:mb-0 z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="space-y-8 text-center lg:text-left"
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-indigo-200 rounded-full px-4 py-2 mb-6 shadow-lg">
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-indigo-700">
                🎨 3D Beauty Experience
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
          >
            <span className="block text-gray-900">IMMERSIVE</span>
            <span className="block bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              BEAUTY
            </span>
            <span className="block text-2xl lg:text-3xl font-normal text-gray-600 mt-4">
              3D Interactive Collection
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-lg leading-relaxed"
          >
            Step into the future of beauty shopping with our immersive 3D
            experience. Interactive products that float and respond to your
            touch.
          </motion.p>

          {/* 3D Features */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 sm:grid-cols-2 gap-4"
          >
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center">
                  <span className="text-xl">🎯</span>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">
                    Interactive 3D
                  </div>
                  <div className="text-xs text-gray-600">Touch & Explore</div>
                </div>
              </div>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-pink-100 to-rose-100 rounded-xl flex items-center justify-center">
                  <span className="text-xl">🌟</span>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">
                    Floating Elements
                  </div>
                  <div className="text-xs text-gray-600">Dynamic Motion</div>
                </div>
              </div>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center">
                  <span className="text-xl">🔮</span>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">
                    Virtual Try-On
                  </div>
                  <div className="text-xs text-gray-600">AR Experience</div>
                </div>
              </div>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center">
                  <span className="text-xl">✨</span>
                </div>
                <div>
                  <div className="text-sm font-semibold text-gray-900">
                    Real-time Effects
                  </div>
                  <div className="text-xs text-gray-600">Live Preview</div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Enter 3D World
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="rounded-full px-8 py-4 border-indigo-300 text-indigo-700 hover:bg-indigo-50"
            >
              Experience Demo
            </Button>
          </motion.div>
        </motion.div>
      </div>

      <div className="w-full lg:w-1/2 flex justify-center z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-lg"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-3xl border border-white/30 shadow-2xl"></div>
            <img
              src="https://images.unsplash.com/photo-1596755389378-c31d21fd1273?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80"
              alt="3D Beauty Products"
              className="relative w-full h-96 object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/20 to-transparent rounded-3xl"></div>
          </div>

          {/* Floating 3D Product Cards */}
          <motion.div
            initial={{ opacity: 0, x: -30, rotateY: -15 }}
            animate={{ opacity: 1, x: 0, rotateY: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
            className="absolute -left-8 top-1/4 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-2xl border border-indigo-100 transform hover:scale-105 transition-transform duration-300"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <span className="text-2xl">💄</span>
              </div>
              <div className="text-sm font-semibold text-gray-900">Premium</div>
              <div className="text-xs text-gray-600">Lipstick</div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30, rotateY: 15 }}
            animate={{ opacity: 1, x: 0, rotateY: 0 }}
            transition={{ delay: 0.8, duration: 0.8 }}
            className="absolute -right-8 bottom-1/4 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-2xl border border-purple-100 transform hover:scale-105 transition-transform duration-300"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                <span className="text-2xl">✨</span>
              </div>
              <div className="text-sm font-semibold text-gray-900">Glow</div>
              <div className="text-xs text-gray-600">Serum</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  </div>
);

function Page() {
  const controls = useAnimation();
  const searchParams = useSearchParams();
  const heroVariant = searchParams.get("hero") || "1"; // Default to variant 1

  useEffect(() => {
    controls.start("visible");
  }, [controls]);

  // Hero variant components
  const renderHeroVariant = () => {
    switch (heroVariant) {
      case "1":
        return <HeroVariant1 controls={controls} />;
      case "2":
        return <HeroVariant2 controls={controls} />;
      case "3":
        return <HeroVariant3 controls={controls} />;
      case "4":
        return <HeroVariant4 controls={controls} />;
      case "5":
        return <HeroVariant5 controls={controls} />;
      case "6":
        return <HeroVariant6 controls={controls} />;
      case "7":
        return <HeroVariant7 controls={controls} />;
      case "8":
        return <HeroVariant8 controls={controls} />;
      case "9":
        return <HeroVariant9 controls={controls} />;
      case "10":
        return <HeroVariant10 controls={controls} />;
      case "11":
        return <HeroVariant11 controls={controls} />;
      case "12":
        return <HeroVariant12 controls={controls} />;
      case "13":
        return <HeroVariant13 controls={controls} />;
      case "14":
        return <HeroVariant14 controls={controls} />;
      case "15":
        return <HeroVariant15 controls={controls} />;
      case "16":
        return <HeroVariant16 controls={controls} />;
      case "17":
        return <HeroVariant17 controls={controls} />;
      case "18":
        return <HeroVariant18 controls={controls} />;
      default:
        return <HeroVariant1 controls={controls} />;
    }
  };

  return (
    <div className="reveal-element">
      {/* Dynamic Hero Section based on query parameter */}
      {renderHeroVariant()}

      {/* Browse by Categories Section */}
      <div className="mx-auto container py-16 px-4 relative">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(100,100,250,0.05),transparent_70%)]"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-14">
            <div className="max-w-xl">
              <div className="inline-flex items-center gap-2 mb-2 bg-indigo-50 px-3 py-1 rounded-full">
                <span className="h-2 w-2 rounded-full bg-indigo-600 animate-pulse"></span>
                <Badge
                  variant="outline"
                  className="text-indigo-600 border-none bg-transparent"
                >
                  Explore Our Premium Collection
                </Badge>
              </div>
              <h3 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-indigo-900 to-gray-700">
                Browse By Categories
              </h3>
              <p className="text-gray-500 mt-4 max-w-md">
                Discover our meticulously curated selection designed to match
                your unique style and performance needs.
              </p>
            </div>
            <Button
              variant="outline"
              className="group mt-6 md:mt-0 h-12 px-6 rounded-full gap-2 border-gray-300 border-2 hover:border-black hover:bg-black hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
            >
              View All Categories
              <span className="relative w-5 h-5 overflow-hidden inline-flex items-center justify-center">
                <ArrowRight className="h-4 w-4 absolute group-hover:translate-x-8 group-hover:opacity-0 transition-all duration-300" />
                <ArrowRight className="h-4 w-4 absolute -translate-x-8 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300" />
              </span>
            </Button>
          </div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 relative"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
            variants={{
              hidden: {},
              visible: {
                transition: {
                  staggerChildren: 0.15,
                },
              },
            }}
          >
            {/* Running Category */}
            <Link href="/categories/running-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-indigo-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M17.5 5.5C17.5 3.01472 15.4853 1 13 1C10.5147 1 8.5 3.01472 8.5 5.5V6.5H17.5V5.5Z"
                            stroke="#4F46E5"
                            strokeWidth="2"
                          />
                          <path
                            d="M4.5 6.5H19.5L18 23H6L4.5 6.5Z"
                            stroke="#4F46E5"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-indigo-700 uppercase tracking-wider">
                        Performance
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-indigo-700 transition-colors duration-300">
                      Running Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Premium performance shoes engineered for speed and
                      endurance
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/656.png";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-indigo-100 flex items-center justify-center text-xs font-medium text-indigo-600">
                          +8
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-indigo-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        24 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-indigo-100 rounded-full overflow-hidden">
                      <div className="h-full w-2/3 bg-indigo-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/656.png"
                    alt="Running Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-indigo-600 hover:bg-indigo-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Casual Category */}
            <Link href="/categories/casual-sneakers">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-rose-500/20 to-pink-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-rose-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-rose-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3 18V6C3 3.79086 4.79086 2 7 2H17C19.2091 2 21 3.79086 21 6V18C21 20.2091 19.2091 22 17 22H7C4.79086 22 3 20.2091 3 18Z"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                          <path
                            d="M15 9.5C15 11.7091 13.2091 13.5 11 13.5C8.79086 13.5 7 11.7091 7 9.5C7 7.29086 8.79086 5.5 11 5.5C13.2091 5.5 15 7.29086 15 9.5Z"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                          <path
                            d="M17.5 20C17.1667 16.8333 15 13.5 11 13.5C7 13.5 4.83333 16.8333 4.5 20"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-rose-700 uppercase tracking-wider">
                          Casual
                        </span>
                        <span className="text-[10px] font-semibold text-white bg-rose-500 px-1.5 py-0.5 rounded-sm">
                          NEW
                        </span>
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-rose-600 transition-colors duration-300">
                      Casual Sneakers
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Stylish comfort for everyday wear with premium materials
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/651.webp";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-rose-100 flex items-center justify-center text-xs font-medium text-rose-600">
                          +12
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-rose-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        36 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-rose-100 rounded-full overflow-hidden">
                      <div className="h-full w-[85%] bg-rose-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/651.webp"
                    alt="Casual Sneakers"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-rose-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-rose-600 hover:bg-rose-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Basketball Category */}
            <Link href="/categories/basketball-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 to-yellow-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-amber-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path
                            d="M12 22C12 17.5 16.5 12 16.5 7"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path
                            d="M12 22C12 17.5 7.5 12 7.5 7"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path d="M2 12H22" stroke="#B45309" strokeWidth="2" />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-amber-700 uppercase tracking-wider">
                        Sports
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-amber-700 transition-colors duration-300">
                      Basketball Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Court-ready performance with enhanced grip and support
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/822.png";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-amber-100 flex items-center justify-center text-xs font-medium text-amber-600">
                          +5
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-amber-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        18 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-amber-100 rounded-full overflow-hidden">
                      <div className="h-full w-[45%] bg-amber-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/822.png"
                    alt="Basketball Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-amber-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-amber-600 hover:bg-amber-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Training Category */}
            <Link href="/categories/training-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-teal-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-emerald-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6 12H18"
                            stroke="#047857"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                          <path
                            d="M12 18V6"
                            stroke="#047857"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                          <path
                            d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                            stroke="#047857"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-emerald-700 uppercase tracking-wider">
                        Training
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-emerald-700 transition-colors duration-300">
                      Training Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Versatile support for intense workouts and cross-training
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/564.webp";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-emerald-100 flex items-center justify-center text-xs font-medium text-emerald-600">
                          +9
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-emerald-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        29 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-emerald-100 rounded-full overflow-hidden">
                      <div className="h-full w-[75%] bg-emerald-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/564.webp"
                    alt="Training Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-emerald-600 hover:bg-emerald-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Brand Story Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-indigo-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-indigo-200 to-purple-200 rounded-full filter blur-3xl opacity-20 -translate-y-1/2 translate-x-1/3"></div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="max-w-6xl mx-auto"
          >
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              {/* Content */}
              <div className="space-y-8">
                <div>
                  <div className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-indigo-100 rounded-full">
                    <Award className="h-4 w-4 text-indigo-600" />
                    <span className="text-sm font-medium text-indigo-700">
                      Our Story
                    </span>
                  </div>
                  <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-indigo-800 to-gray-700 bg-clip-text text-transparent">
                    Crafting Excellence Since 2020
                  </h2>
                  <p className="text-lg text-gray-600 leading-relaxed mb-6">
                    Born from a passion for quality and innovation, Forever has
                    been at the forefront of premium lifestyle products. We
                    believe that exceptional design should be accessible to
                    everyone, which is why we've dedicated ourselves to creating
                    products that seamlessly blend style, comfort, and
                    sustainability.
                  </p>
                  <p className="text-gray-600 leading-relaxed">
                    Our journey began with a simple mission: to revolutionize
                    the way people experience everyday essentials. Today, we're
                    proud to serve over 100,000 customers worldwide, each one
                    part of our growing community of style enthusiasts.
                  </p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                    className="text-center"
                  >
                    <div className="text-3xl font-bold text-indigo-600 mb-2">
                      100K+
                    </div>
                    <div className="text-sm text-gray-600">Happy Customers</div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.3, duration: 0.6 }}
                    className="text-center"
                  >
                    <div className="text-3xl font-bold text-indigo-600 mb-2">
                      50+
                    </div>
                    <div className="text-sm text-gray-600">
                      Countries Served
                    </div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                    className="text-center"
                  >
                    <div className="text-3xl font-bold text-indigo-600 mb-2">
                      4.9★
                    </div>
                    <div className="text-sm text-gray-600">Average Rating</div>
                  </motion.div>
                </div>

                <div className="flex flex-wrap gap-4">
                  <Button className="bg-black hover:bg-gray-800 text-white rounded-full px-8 py-3">
                    Learn More About Us
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full px-8 py-3 border-gray-300"
                  >
                    <Play className="mr-2 h-4 w-4" />
                    Watch Our Story
                  </Button>
                </div>
              </div>

              {/* Visual */}
              <div className="relative">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="relative"
                >
                  {/* Main Image */}
                  <div className="relative rounded-3xl overflow-hidden shadow-2xl">
                    <img
                      src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                      alt="Our Story"
                      className="w-full h-96 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  </div>

                  {/* Floating Cards */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.6, duration: 0.6 }}
                    className="absolute -bottom-6 -left-6 bg-white rounded-2xl shadow-xl p-6 max-w-[200px]"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">
                          Eco-Friendly
                        </div>
                        <div className="text-xs text-gray-500">
                          100% Sustainable
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                    className="absolute -top-6 -right-6 bg-white rounded-2xl shadow-xl p-6 max-w-[200px]"
                  >
                    <div className="text-center">
                      <div className="text-2xl font-bold text-indigo-600 mb-1">
                        4+ Years
                      </div>
                      <div className="text-sm text-gray-600">Of Innovation</div>
                      <div className="flex justify-center mt-2">
                        <div className="flex -space-x-1">
                          {[1, 2, 3].map((i) => (
                            <div
                              key={i}
                              className="w-6 h-6 rounded-full border-2 border-white overflow-hidden"
                            >
                              <img
                                src={`https://randomuser.me/api/portraits/men/${
                                  i + 20
                                }.jpg`}
                                alt="Team member"
                                className="w-full h-full object-cover"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-between mb-8">
          <h3 className="text-3xl font-bold">Latest Arrivals</h3>
          <Button variant="outline" className="gap-2 border-gray-300">
            View All Products <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
          {product.map((item) => (
            <Link
              key={item.name}
              href={`/products/${item.name.replaceAll(" ", "-").toLowerCase()}`}
              className="group bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px] cursor-pointer border border-gray-100"
            >
              <InView>
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-72 object-cover object-center transition-all duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute top-3 left-3 flex gap-2">
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg">
                      NEW
                    </span>
                    <span className="bg-red-500 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg">
                      -20%
                    </span>
                  </div>
                  <div className="absolute right-3 top-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors">
                      <Heart className="h-4 w-4 text-gray-700" />
                    </button>
                    <button className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors">
                      <ShoppingBag className="h-4 w-4 text-gray-700" />
                    </button>
                  </div>
                </div>
                <div className="p-5">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className="h-4 w-4 text-amber-400 fill-amber-400"
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      120 reviews
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                    Premium quality skincare product for all skin types
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-gray-900">
                        {item.price} MAD
                      </span>
                      <span className="text-sm text-gray-400 line-through">
                        130.00 MAD
                      </span>
                    </div>
                    <div className="bg-indigo-50 text-indigo-600 text-xs font-medium px-2 py-1 rounded">
                      In Stock
                    </div>
                  </div>
                </div>
              </InView>
            </Link>
          ))}
        </div>
        <div className="py-6 flex items-center justify-center">
          <Button variant={"default"} className="rounded-full group gap-1">
            Load More{" "}
            <ArrowRight className="h-4 w-4 group-hover:translate-x-2 transition-transform" />
          </Button>
        </div>
      </div>

      {/* Features/Benefits Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-indigo-100 rounded-full">
              <Shield className="h-4 w-4 text-indigo-600" />
              <span className="text-sm font-medium text-indigo-700">
                Why Choose Forever
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-indigo-800 to-gray-700 bg-clip-text text-transparent">
              Experience the Difference
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We're committed to delivering exceptional quality and service that
              goes beyond your expectations
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.6 }}
              className="group text-center"
            >
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Truck className="h-10 w-10 text-white" />
                </div>
                <div className="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">
                Free Shipping
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Enjoy free shipping on all orders over $50. Fast, reliable
                delivery to your doorstep.
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="group text-center"
            >
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <RotateCcw className="h-10 w-10 text-white" />
                </div>
                <div className="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">
                30-Day Returns
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Not satisfied? Return any item within 30 days for a full refund,
                no questions asked.
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="group text-center"
            >
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Award className="h-10 w-10 text-white" />
                </div>
                <div className="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">
                Premium Quality
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Every product is carefully crafted with the finest materials and
                attention to detail.
              </p>
            </motion.div>

            {/* Feature 4 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="group text-center"
            >
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <div className="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">
                24/7 Support
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Our dedicated support team is here to help you anytime,
                anywhere.
              </p>
            </motion.div>
          </div>

          {/* Additional Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="mt-20 bg-gradient-to-r from-gray-50 to-indigo-50 rounded-3xl p-8 md:p-12"
          >
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold mb-6 text-gray-900">
                  More Than Just Products
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">
                        Sustainable Materials
                      </h4>
                      <p className="text-gray-600">
                        Eco-friendly production with recycled and sustainable
                        materials
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">
                        Ethical Manufacturing
                      </h4>
                      <p className="text-gray-600">
                        Fair trade practices and ethical working conditions
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">
                        Community Impact
                      </h4>
                      <p className="text-gray-600">
                        Supporting local communities and charitable initiatives
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80"
                  alt="Sustainable practices"
                  className="rounded-2xl shadow-lg w-full h-80 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Customer Testimonials Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/testimonials-pattern.svg')] opacity-5"></div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-indigo-100 rounded-full">
              <Quote className="h-4 w-4 text-indigo-600" />
              <span className="text-sm font-medium text-indigo-700">
                Customer Stories
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-indigo-800 to-gray-700 bg-clip-text text-transparent">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what real customers have
              to say about their Forever experience.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.6 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-amber-400 fill-amber-400"
                  />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "The quality is absolutely amazing! I've been wearing my Forever
                shoes for months and they still look brand new. The comfort
                level is unmatched."
              </blockquote>
              <div className="flex items-center gap-4">
                <img
                  src="https://randomuser.me/api/portraits/women/32.jpg"
                  alt="Sarah Johnson"
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    Sarah Johnson
                  </div>
                  <div className="text-sm text-gray-500">Verified Customer</div>
                </div>
              </div>
            </motion.div>

            {/* Testimonial 2 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-amber-400 fill-amber-400"
                  />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "Fast shipping, excellent customer service, and the products
                exceeded my expectations. Forever has become my go-to brand for
                premium lifestyle products."
              </blockquote>
              <div className="flex items-center gap-4">
                <img
                  src="https://randomuser.me/api/portraits/men/45.jpg"
                  alt="Michael Chen"
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    Michael Chen
                  </div>
                  <div className="text-sm text-gray-500">Verified Customer</div>
                </div>
              </div>
            </motion.div>

            {/* Testimonial 3 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-amber-400 fill-amber-400"
                  />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "I love how sustainable and ethically made their products are.
                It feels good to support a brand that cares about the
                environment and quality."
              </blockquote>
              <div className="flex items-center gap-4">
                <img
                  src="https://randomuser.me/api/portraits/women/28.jpg"
                  alt="Emma Rodriguez"
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    Emma Rodriguez
                  </div>
                  <div className="text-sm text-gray-500">Verified Customer</div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="mt-16 text-center"
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-4xl mx-auto">
              <div className="grid md:grid-cols-4 gap-8 items-center">
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600 mb-2">
                    4.9/5
                  </div>
                  <div className="text-sm text-gray-600">Average Rating</div>
                  <div className="flex justify-center mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 text-amber-400 fill-amber-400"
                      />
                    ))}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600 mb-2">
                    25K+
                  </div>
                  <div className="text-sm text-gray-600">Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600 mb-2">
                    98%
                  </div>
                  <div className="text-sm text-gray-600">Satisfaction Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600 mb-2">
                    24/7
                  </div>
                  <div className="text-sm text-gray-600">Customer Support</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="relative rounded-3xl overflow-hidden">
          {/* Background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-50 via-gray-50 to-pink-50"></div>
          <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-b from-pink-200 to-indigo-200 rounded-full filter blur-3xl opacity-20 -translate-y-1/2 translate-x-1/3"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-t from-indigo-200 to-purple-200 rounded-full filter blur-3xl opacity-20 translate-y-1/2 -translate-x-1/3"></div>

          <div className="relative z-10 p-8 md:p-12 lg:p-16 flex flex-col lg:flex-row items-center justify-between">
            <div className="mb-10 lg:mb-0 lg:mr-8 max-w-md">
              <div className="inline-flex items-center gap-2 mb-4 px-3 py-1 bg-black/5 rounded-full">
                <span className="flex h-2 w-2 rounded-full bg-indigo-600 animate-pulse"></span>
                <span className="text-xs font-medium text-gray-700">
                  Join 25,000+ subscribers
                </span>
              </div>

              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-indigo-800 to-gray-800">
                Stay in the loop.
              </h2>

              <p className="text-lg sm:text-xl text-gray-600 mb-8">
                Get exclusive access to limited drops, special offers and
                insider news about upcoming releases.
              </p>

              <motion.form
                className="space-y-5 w-full max-w-md"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col space-y-2">
                  <label
                    htmlFor="subscriber-email"
                    className="text-sm font-medium text-gray-700"
                  >
                    Email address
                  </label>
                  <div className="relative">
                    <input
                      id="subscriber-email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-20 transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="text-gray-400"
                      >
                        <path
                          d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C19.5304 19 20.0391 18.7893 20.4142 18.4142C20.7893 18.0391 21 17.5304 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3.96086 5.21071 3.58579 5.58579C3.21071 5.96086 3 6.46957 3 7V17C3 18.1046 3.89543 19 5 19Z"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <div>
                  <Button className="w-full bg-black hover:bg-gray-800 text-white font-medium rounded-xl py-6 transition-all duration-300 shadow-sm hover:shadow-md">
                    Subscribe to Newsletter
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-2"
                    >
                      <path
                        d="M5 12H19M19 12L12 5M19 12L12 19"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="marketing-consent"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="marketing-consent"
                    className="text-sm text-gray-600"
                  >
                    I agree to receive emails about new collections and
                    exclusive offers.
                  </label>
                </div>

                <div className="flex items-center gap-4 pt-2">
                  <div className="flex -space-x-2">
                    {[1, 2, 3, 4].map((i) => (
                      <div
                        key={i}
                        className="h-6 w-6 rounded-full border-2 border-white overflow-hidden"
                      >
                        <img
                          src={`https://randomuser.me/api/portraits/${
                            i % 2 === 0 ? "women" : "men"
                          }/${i + 10}.jpg`}
                          alt="Subscriber"
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500">
                    Join thousands of happy subscribers
                  </p>
                </div>
              </motion.form>
            </div>

            <div className="relative w-full max-w-sm lg:max-w-md xl:max-w-lg">
              <motion.div
                initial={{ opacity: 0, scale: 0.9, rotate: 5 }}
                whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="relative z-10"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-pink-500/30 rounded-3xl transform rotate-3 scale-105 blur-xl opacity-40"></div>
                <img
                  src="/955.png"
                  alt="Premium shoes"
                  className="relative z-10 w-full h-auto max-h-80 object-contain transform -rotate-6 hover:rotate-0 transition-transform duration-700 drop-shadow-xl"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20, y: 20 }}
                whileInView={{ opacity: 1, x: 0, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="absolute bottom-4 -left-4 bg-white rounded-xl shadow-xl p-3 max-w-[180px] z-20 border border-gray-100"
              >
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21 5L11 5M21 12L8 12M21 19L14 19"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Exclusive Content</h4>
                    <p className="text-xs text-gray-500">
                      For subscribers only
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20, y: -20 }}
                whileInView={{ opacity: 1, x: 0, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="absolute top-10 -right-4 bg-white rounded-xl shadow-xl p-3 max-w-[180px] z-20 border border-gray-100"
              >
                <div className="text-center">
                  <div className="inline-block mb-1 bg-indigo-100 text-indigo-700 text-xs px-2 py-1 rounded-full">
                    Weekly Updates
                  </div>
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <span className="text-amber-500">★★★★★</span>
                    <span className="text-xs font-medium">4.9/5</span>
                  </div>
                  <p className="text-xs text-gray-500">Rated by subscribers</p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Brand Partners & Trust Section */}
      <section className="py-16 bg-gray-50 border-t border-gray-200">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
              Trusted by Industry Leaders
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We're proud to partner with leading brands and organizations that
              share our commitment to quality and innovation.
            </p>
          </motion.div>

          {/* Trust Badges */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center mb-16"
          >
            {/* SSL Certificate */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-green-200 transition-colors">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                SSL Secured
              </span>
            </div>

            {/* Money Back Guarantee */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors">
                <RotateCcw className="h-8 w-8 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                Money Back
              </span>
            </div>

            {/* Fast Shipping */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-purple-200 transition-colors">
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                Fast Delivery
              </span>
            </div>

            {/* Quality Assured */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-amber-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-amber-200 transition-colors">
                <Award className="h-8 w-8 text-amber-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                Quality Assured
              </span>
            </div>

            {/* Eco Friendly */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-emerald-200 transition-colors">
                <Leaf className="h-8 w-8 text-emerald-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                Eco Friendly
              </span>
            </div>

            {/* Global Shipping */}
            <div className="flex flex-col items-center text-center group">
              <div className="w-16 h-16 bg-indigo-100 rounded-2xl flex items-center justify-center mb-3 group-hover:bg-indigo-200 transition-colors">
                <Globe className="h-8 w-8 text-indigo-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                Global Shipping
              </span>
            </div>
          </motion.div>

          {/* Payment Methods */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="text-center"
          >
            <h4 className="text-lg font-semibold text-gray-900 mb-6">
              Secure Payment Methods
            </h4>
            <div className="flex flex-wrap justify-center items-center gap-6 opacity-60">
              {/* Payment method logos would go here */}
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 min-w-[80px] h-12 flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  VISA
                </span>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 min-w-[80px] h-12 flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  MASTERCARD
                </span>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 min-w-[80px] h-12 flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  PAYPAL
                </span>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 min-w-[80px] h-12 flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  APPLE PAY
                </span>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 min-w-[80px] h-12 flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  GOOGLE PAY
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Instagram Feed Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-pink-100 rounded-full">
              <Instagram className="h-4 w-4 text-pink-600" />
              <span className="text-sm font-medium text-pink-700">
                Follow Us
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-pink-800 to-gray-700 bg-clip-text text-transparent">
              #ForeverStyle
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Join our community and share your Forever moments. Tag us @forever
              for a chance to be featured!
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((item) => (
              <motion.div
                key={item}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: item * 0.05, duration: 0.4 }}
                className="group relative aspect-square overflow-hidden rounded-2xl cursor-pointer"
              >
                <img
                  src={`https://picsum.photos/300/300?random=${item + 50}`}
                  alt={`Instagram post ${item}`}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                  <Instagram className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="text-center mt-12"
          >
            <Button className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white rounded-full px-8 py-3">
              <Instagram className="mr-2 h-5 w-5" />
              Follow @Forever
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

export default Page;
