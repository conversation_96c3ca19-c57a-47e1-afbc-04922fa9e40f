import { ChartDasboard } from "@/components/DashboardCharts";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar } from "@/components/ui/scroll-area";

export default function Page() {
  return (
    <div className="flex ">
      <ScrollArea className="w-[285px] border-r h-screen">
        <ScrollBar />
        <div className="3xl:mt-6 mt-4 pb-3">
          <h6 className="rizzui-title-h6 mb-2 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Overview
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-200 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M128,80,98.13,102.4a8,8,0,0,1-4.8,1.6H32V64a8,8,0,0,1,8-8H93.33a8,8,0,0,1,4.8,1.6Z"
                    opacity="0.2"
                  />
                  <path d="M216,72H130.67L102.93,51.2a16.12,16.12,0,0,0-9.6-3.2H40A16,16,0,0,0,24,64V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V88A16,16,0,0,0,216,72ZM40,64H93.33l21.34,16L93.33,96H40ZM216,200H40V112H93.33a16.12,16.12,0,0,0,9.6-3.2L130.67,88H216Z" />
                </svg>
              </span>
              <span className="truncate">File Manager</span>
            </div>
          </a>
          <a
            className="before:top-2/5 text-primary before:bg-primary group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize before:absolute before:-start-3 before:block before:h-4/5 before:w-1 before:rounded-ee-md before:rounded-se-md lg:my-1 2xl:mx-5 2xl:my-2 2xl:before:-start-5"
            href="/executive"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] text-primary me-2 inline-flex h-5 w-5 items-center justify-center rounded-md">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,118.31V200a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V118.31h0A191.14,191.14,0,0,0,128,144,191.08,191.08,0,0,0,224,118.31Z"
                    opacity="0.2"
                  />
                  <path d="M104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112ZM232,72V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V72A16,16,0,0,1,40,56H80V48a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24v8h40A16,16,0,0,1,232,72ZM96,56h64V48a8,8,0,0,0-8-8H104a8,8,0,0,0-8,8ZM40,72v41.62A184.07,184.07,0,0,0,128,136a184,184,0,0,0,88-22.39V72ZM216,200V131.63A200.25,200.25,0,0,1,128,152a200.19,200.19,0,0,1-88-20.36V200H216Z" />
                </svg>
              </span>
              <span className="truncate">Executive</span>
            </div>
            <span className="rizzui-badge color font-lexend border-red bg-red-lighter text-red-dark dark:bg-red inline-flex items-center justify-center rounded-full border bg-opacity-50 px-2 py-0.5 text-xs font-normal capitalize tracking-wider duration-200 dark:bg-opacity-40 dark:text-gray-900 dark:text-opacity-90 dark:backdrop-blur">
              New
            </span>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/financial"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z"
                    opacity="0.2"
                  />
                  <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm40-68a28,28,0,0,1-28,28h-4v8a8,8,0,0,1-16,0v-8H104a8,8,0,0,1,0-16h36a12,12,0,0,0,0-24H116a28,28,0,0,1,0-56h4V72a8,8,0,0,1,16,0v8h16a8,8,0,0,1,0,16H116a12,12,0,0,0,0,24h24A28,28,0,0,1,168,148Z" />
                </svg>
              </span>
              <span className="truncate">Financial</span>
            </div>
            <span className="rizzui-badge color font-lexend border-red bg-red-lighter text-red-dark dark:bg-red inline-flex items-center justify-center rounded-full border bg-opacity-50 px-2 py-0.5 text-xs font-normal capitalize tracking-wider duration-200 dark:bg-opacity-40 dark:text-gray-900 dark:text-opacity-90 dark:backdrop-blur">
              New
            </span>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/logistics"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M128,129.09V232a8,8,0,0,1-3.84-1l-88-48.18a8,8,0,0,1-4.16-7V80.18a8,8,0,0,1,.7-3.25Z"
                    opacity="0.2"
                  />
                  <path d="M223.68,66.15,135.68,18a15.88,15.88,0,0,0-15.36,0l-88,48.17a16,16,0,0,0-8.32,14v95.64a16,16,0,0,0,8.32,14l88,48.17a15.88,15.88,0,0,0,15.36,0l88-48.17a16,16,0,0,0,8.32-14V80.18A16,16,0,0,0,223.68,66.15ZM128,32l80.34,44-29.77,16.3-80.35-44ZM128,120,47.66,76l33.9-18.56,80.34,44ZM40,90l80,43.78v85.79L40,175.82Zm176,85.78h0l-80,43.79V133.82l32-17.51V152a8,8,0,0,0,16,0V107.55L216,90v85.77Z" />
                </svg>
              </span>
              <span className="truncate">Logistics</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/ecommerce"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M216,64l-12.16,66.86A16,16,0,0,1,188.1,144H62.55L48,64Z"
                    opacity="0.2"
                  />
                  <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z" />
                </svg>
              </span>
              <span className="truncate">E-Commerce</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/analytics"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M208,40V208H152V40Z" opacity="0.2" />
                  <path d="M224,200h-8V40a8,8,0,0,0-8-8H152a8,8,0,0,0-8,8V80H96a8,8,0,0,0-8,8v40H48a8,8,0,0,0-8,8v64H32a8,8,0,0,0,0,16H224a8,8,0,0,0,0-16ZM160,48h40V200H160ZM104,96h40V200H104ZM56,144H88v56H56Z" />
                </svg>
              </span>
              <span className="truncate">Analytics</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/support"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M80,144v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V128H64A16,16,0,0,1,80,144Zm112-16a16,16,0,0,0-16,16v40a16,16,0,0,0,16,16h32V128Z"
                    opacity="0.2"
                  />
                  <path d="M201.89,54.66A104.08,104.08,0,0,0,24,128v56a24,24,0,0,0,24,24H64a24,24,0,0,0,24-24V144a24,24,0,0,0-24-24H40.36A88.12,88.12,0,0,1,190.54,65.93,87.39,87.39,0,0,1,215.65,120H192a24,24,0,0,0-24,24v40a24,24,0,0,0,24,24h24a24,24,0,0,1-24,24H136a8,8,0,0,0,0,16h56a40,40,0,0,0,40-40V128A103.41,103.41,0,0,0,201.89,54.66ZM64,136a8,8,0,0,1,8,8v40a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V136Zm128,56a8,8,0,0,1-8-8V144a8,8,0,0,1,8-8h24v56Z" />
                </svg>
              </span>
              <span className="truncate">Support</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Apps Kit
          </h6>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M216,64l-12.16,66.86A16,16,0,0,1,188.1,144H62.55L48,64Z"
                      opacity="0.2"
                    />
                    <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z" />
                  </svg>
                </span>
                E-Commerce
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/products"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Products</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/products/FC6723757651DB74"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Product Details</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/products/create"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Create Product</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/products/FC6723757651DB74/edit"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Edit Product</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/categories"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Categories</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/categories/create"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Create Category</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/categories/FC6723757651DB74/edit"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Edit Category</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/orders"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Orders</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/orders/FC6723757651DB74"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Order Details</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/orders/create"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Create Order</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/orders/FC6723757651DB74/edit"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Edit Order</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/reviews"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Reviews</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/shop"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Shop</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/cart"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Cart</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/ecommerce/checkout"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Checkout &amp; Payment</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M80,144v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V128H64A16,16,0,0,1,80,144Zm112-16a16,16,0,0,0-16,16v40a16,16,0,0,0,16,16h32V128Z"
                      opacity="0.2"
                    />
                    <path d="M201.89,54.66A104.08,104.08,0,0,0,24,128v56a24,24,0,0,0,24,24H64a24,24,0,0,0,24-24V144a24,24,0,0,0-24-24H40.36A88.12,88.12,0,0,1,190.54,65.93,87.39,87.39,0,0,1,215.65,120H192a24,24,0,0,0-24,24v40a24,24,0,0,0,24,24h24a24,24,0,0,1-24,24H136a8,8,0,0,0,0,16h56a40,40,0,0,0,40-40V128A103.41,103.41,0,0,0,201.89,54.66ZM64,136a8,8,0,0,1,8,8v40a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V136Zm128,56a8,8,0,0,1-8-8V144a8,8,0,0,1,8-8h24v56Z" />
                  </svg>
                </span>
                Support
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/support/inbox"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Inbox</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/support/snippets"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Snippets</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/support/templates"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Templates</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M192,168a40,40,0,0,1-40,40H128V128h24A40,40,0,0,1,192,168ZM112,48a40,40,0,0,0,0,80h16V48Z"
                      opacity="0.2"
                    />
                    <path d="M152,120H136V56h8a32,32,0,0,1,32,32,8,8,0,0,0,16,0,48.05,48.05,0,0,0-48-48h-8V24a8,8,0,0,0-16,0V40h-8a48,48,0,0,0,0,96h8v64H104a32,32,0,0,1-32-32,8,8,0,0,0-16,0,48.05,48.05,0,0,0,48,48h16v16a8,8,0,0,0,16,0V216h16a48,48,0,0,0,0-96Zm-40,0a32,32,0,0,1,0-64h8v64Zm40,80H136V136h16a32,32,0,0,1,0,64Z" />
                  </svg>
                </span>
                Invoice
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/invoice"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">List</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/invoice/FC6723757651DB74"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Details</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/invoice/create"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Create</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/invoice/FC6723757651DB74/edit"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Edit</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M128,129.09V232a8,8,0,0,1-3.84-1l-88-48.18a8,8,0,0,1-4.16-7V80.18a8,8,0,0,1,.7-3.25Z"
                      opacity="0.2"
                    />
                    <path d="M223.68,66.15,135.68,18a15.88,15.88,0,0,0-15.36,0l-88,48.17a16,16,0,0,0-8.32,14v95.64a16,16,0,0,0,8.32,14l88,48.17a15.88,15.88,0,0,0,15.36,0l88-48.17a16,16,0,0,0,8.32-14V80.18A16,16,0,0,0,223.68,66.15ZM128,32l80.34,44-29.77,16.3-80.35-44ZM128,120,47.66,76l33.9-18.56,80.34,44ZM40,90l80,43.78v85.79L40,175.82Zm176,85.78h0l-80,43.79V133.82l32-17.51V152a8,8,0,0,0,16,0V107.55L216,90v85.77Z" />
                  </svg>
                </span>
                Logistics
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/shipments"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Shipment List</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/shipments/FC6723757651DB74"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Shipment Details</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/shipments/create"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Create Shipment</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/shipments/FC6723757651DB74/edit"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Edit Shipment</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/customer-profile"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Customer Profile</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/logistics/tracking/FC6723757651DB74"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Tracking</span>
                </div>
              </a>
            </div>
          </div>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/file-manager"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M128,80,98.13,102.4a8,8,0,0,1-4.8,1.6H32V64a8,8,0,0,1,8-8H93.33a8,8,0,0,1,4.8,1.6Z"
                    opacity="0.2"
                  />
                  <path d="M216,72H130.67L102.93,51.2a16.12,16.12,0,0,0-9.6-3.2H40A16,16,0,0,0,24,64V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V88A16,16,0,0,0,216,72ZM40,64H93.33l21.34,16L93.33,96H40ZM216,200H40V112H93.33a16.12,16.12,0,0,0,9.6-3.2L130.67,88H216Z" />
                </svg>
              </span>
              <span className="truncate">File Manager</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/event-calendar"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M216,48V88H40V48a8,8,0,0,1,8-8H208A8,8,0,0,1,216,48Z"
                    opacity="0.2"
                  />
                  <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-48-56a8,8,0,0,1-8,8H136v16a8,8,0,0,1-16,0V160H104a8,8,0,0,1,0-16h16V128a8,8,0,0,1,16,0v16h16A8,8,0,0,1,160,152Z" />
                </svg>
              </span>
              <span className="truncate">Event Calendar</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/roles-permissions"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,168v40H152V168ZM92.69,48H40a8,8,0,0,0-8,8V80h96L98.34,50.34A8,8,0,0,0,92.69,48Z"
                    opacity="0.2"
                  />
                  <path d="M224,160h-8v-4a28,28,0,0,0-56,0v4h-8a8,8,0,0,0-8,8v40a8,8,0,0,0,8,8h72a8,8,0,0,0,8-8V168A8,8,0,0,0,224,160Zm-48-4a12,12,0,0,1,24,0v4H176Zm40,44H160V176h56Zm0-128H131.31L104,44.69A15.86,15.86,0,0,0,92.69,40H40A16,16,0,0,0,24,56V200.62A15.4,15.4,0,0,0,39.38,216h73.18a8,8,0,0,0,0-16H40V88H216v16a8,8,0,0,0,16,0V88A16,16,0,0,0,216,72ZM92.69,56l16,16H40V56Z" />
                </svg>
              </span>
              <span className="truncate">Roles &amp; Permissions</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/point-of-sale"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M232,96v96a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V96Z"
                    opacity="0.2"
                  />
                  <path d="M224,48H32A16,16,0,0,0,16,64V192a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V64A16,16,0,0,0,224,48Zm0,16V88H32V64Zm0,128H32V104H224v88Zm-16-24a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h32A8,8,0,0,1,208,168Zm-64,0a8,8,0,0,1-8,8H120a8,8,0,0,1,0-16h16A8,8,0,0,1,144,168Z" />
                </svg>
              </span>
              <span className="truncate">Point of Sale</span>
            </div>
            <span className="rizzui-badge color font-lexend border-green bg-green-lighter text-green-dark dark:bg-green inline-flex items-center justify-center rounded-full border bg-opacity-50 px-2 py-0.5 text-xs font-normal capitalize tracking-wider duration-200 dark:bg-opacity-40 dark:text-gray-900 dark:text-opacity-90 dark:backdrop-blur">
              Update
            </span>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Search &amp; Filters
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/search/real-estate"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M216,115.54V216H152V160a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v56H40V115.54a8,8,0,0,1,2.62-5.92l80-75.54a8,8,0,0,1,10.77,0l80,75.54A8,8,0,0,1,216,115.54Z"
                    opacity="0.2"
                  />
                  <path d="M240,208H224V115.55a16,16,0,0,0-5.17-11.78l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM48,115.55l.11-.1L128,40l79.9,75.43.11.1V208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48ZM144,208H112V160h32Z" />
                </svg>
              </span>
              <span className="truncate">Real Estate</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/search/flight"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M209,81l-33,31,32,88-24,24-48-72-24,24v24L88,224,72,184,32,168l24-24H80l24-24L32,72,56,48l88,32,31-33A24,24,0,0,1,209,81Z"
                    opacity="0.2"
                  />
                  <path d="M185.33,114.21l29.14-27.43.17-.16a32,32,0,0,0-45.26-45.26l-.16.17L141.79,70.67l-83-30.2a8,8,0,0,0-8.39,1.86l-24,24a8,8,0,0,0,1.22,12.31l63.89,42.59L76.69,136H56a8,8,0,0,0-5.65,2.34l-24,24A8,8,0,0,0,29,175.42l36.82,14.73,14.7,36.75.06.16a8,8,0,0,0,13.18,2.47l23.87-23.88A8,8,0,0,0,120,200V179.31l14.76-14.76,42.59,63.89a8,8,0,0,0,12.31,1.22l24-24a8,8,0,0,0,1.86-8.39Zm-.07,97.23-42.59-63.89A8,8,0,0,0,136.8,144a7.09,7.09,0,0,0-.79,0,8,8,0,0,0-5.66,2.34l-24,24A8,8,0,0,0,104,176v20.69L90.93,209.76,79.43,181A8,8,0,0,0,75,176.57l-28.74-11.5L59.32,152H80a8,8,0,0,0,5.66-2.34l24-24a8,8,0,0,0-1.22-12.32L44.56,70.74l13.5-13.49,83.22,30.26a8,8,0,0,0,8.56-2l30.94-32.88A16,16,0,0,1,203.4,75.22l-32.87,30.94a8,8,0,0,0-2,8.56l30.26,83.22Z" />
                </svg>
              </span>
              <span className="truncate">Flight Booking</span>
            </div>
            <span className="rizzui-badge color font-lexend border-green bg-green-lighter text-green-dark dark:bg-green inline-flex items-center justify-center rounded-full border bg-opacity-50 px-2 py-0.5 text-xs font-normal capitalize tracking-wider duration-200 dark:bg-opacity-40 dark:text-gray-900 dark:text-opacity-90 dark:backdrop-blur">
              Update
            </span>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/search/nft"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M232,104c0,24-40,48-104,48S24,128,24,104,64,56,128,56,232,80,232,104Z"
                    opacity="0.2"
                  />
                  <path d="M207.58,63.84C186.85,53.48,159.33,48,128,48S69.15,53.48,48.42,63.84,16,88.78,16,104v48c0,15.22,11.82,29.85,32.42,40.16S96.67,208,128,208s58.85-5.48,79.58-15.84S240,167.22,240,152V104C240,88.78,228.18,74.15,207.58,63.84ZM128,64c62.64,0,96,23.23,96,40s-33.36,40-96,40-96-23.23-96-40S65.36,64,128,64Zm-8,95.86v32c-19-.62-35-3.42-48-7.49V153.05A203.43,203.43,0,0,0,120,159.86Zm16,0a203.43,203.43,0,0,0,48-6.81v31.31c-13,4.07-29,6.87-48,7.49ZM32,152V133.53a82.88,82.88,0,0,0,16.42,10.63c2.43,1.21,5,2.35,7.58,3.43V178C40.17,170.16,32,160.29,32,152Zm168,26V147.59c2.61-1.08,5.15-2.22,7.58-3.43A82.88,82.88,0,0,0,224,133.53V152C224,160.29,215.83,170.16,200,178Z" />
                </svg>
              </span>
              <span className="truncate">NFT</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Widgets
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/widgets/cards"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M112,56v48a8,8,0,0,1-8,8H56a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8h48A8,8,0,0,1,112,56Zm88-8H152a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V56A8,8,0,0,0,200,48Zm-96,96H56a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V152A8,8,0,0,0,104,144Zm96,0H152a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V152A8,8,0,0,0,200,144Z"
                    opacity="0.2"
                  />
                  <path d="M200,136H152a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V152A16,16,0,0,0,200,136Zm0,64H152V152h48v48ZM104,40H56A16,16,0,0,0,40,56v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V56A16,16,0,0,0,104,40Zm0,64H56V56h48v48Zm96-64H152a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V56A16,16,0,0,0,200,40Zm0,64H152V56h48v48Zm-96,32H56a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V152A16,16,0,0,0,104,136Zm0,64H56V152h48v48Z" />
                </svg>
              </span>
              <span className="truncate">Cards</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/widgets/icons"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M215.8,119.6l-69.26,70.06a8,8,0,0,1-5.65,2.34H64.2V115.31a8,8,0,0,1,2.34-5.65L112.2,64.52V144l24-24Z"
                    opacity="0.2"
                  />
                  <path d="M221.28,34.75a64,64,0,0,0-90.49,0L60.69,104A15.9,15.9,0,0,0,56,115.31v73.38L26.34,218.34a8,8,0,0,0,11.32,11.32L67.32,200H140.7A15.92,15.92,0,0,0,152,195.32l0,0,69.23-70A64,64,0,0,0,221.28,34.75ZM142.07,46.06A48,48,0,0,1,211.79,112H155.33l34.35-34.34a8,8,0,0,0-11.32-11.32L120,124.69V67.87ZM72,115.35l32-31.67v57l-32,32ZM140.7,184H83.32l56-56h56.74Z" />
                </svg>
              </span>
              <span className="truncate">Icons</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/widgets/charts"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,56V208H32V48H216A8,8,0,0,1,224,56Z"
                    opacity="0.2"
                  />
                  <path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0V156.69l50.34-50.35a8,8,0,0,1,11.32,0L128,132.69,180.69,80H160a8,8,0,0,1,0-16h40a8,8,0,0,1,8,8v40a8,8,0,0,1-16,0V91.31l-58.34,58.35a8,8,0,0,1-11.32,0L96,123.31l-56,56V200H224A8,8,0,0,1,232,208Z" />
                </svg>
              </span>
              <span className="truncate">Charts</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/widgets/maps"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M128,24a80,80,0,0,0-80,80c0,72,80,128,80,128s80-56,80-128A80,80,0,0,0,128,24Zm0,112a32,32,0,1,1,32-32A32,32,0,0,1,128,136Z"
                    opacity="0.2"
                  />
                  <path d="M200,224H150.54A266.56,266.56,0,0,0,174,200.25c27.45-31.57,42-64.85,42-96.25a88,88,0,0,0-176,0c0,31.4,14.51,64.68,42,96.25A266.56,266.56,0,0,0,105.46,224H56a8,8,0,0,0,0,16H200a8,8,0,0,0,0-16ZM56,104a72,72,0,0,1,144,0c0,57.23-55.47,105-72,118C111.47,209,56,161.23,56,104Zm112,0a40,40,0,1,0-40,40A40,40,0,0,0,168,104Zm-64,0a24,24,0,1,1,24,24A24,24,0,0,1,104,104Z" />
                </svg>
              </span>
              <span className="truncate">Maps</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/email-templates"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M224,56l-96,88L32,56Z" opacity="0.2" />
                  <path d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48Zm-96,85.15L52.57,64H203.43ZM98.71,128,40,181.81V74.19Zm11.84,10.85,12,11.05a8,8,0,0,0,10.82,0l12-11.05,58,53.15H52.57ZM157.29,128,216,74.18V181.82Z" />
                </svg>
              </span>
              <span className="truncate">Email Templates</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Forms
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/forms/profile-settings"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M168,100a60,60,0,1,1-60-60A60,60,0,0,1,168,100Z"
                    opacity="0.2"
                  />
                  <path d="M144,157.68a68,68,0,1,0-71.9,0c-20.65,6.76-39.23,19.39-54.17,37.17a8,8,0,1,0,12.24,10.3C50.25,181.19,77.91,168,108,168s57.75,13.19,77.87,37.15a8,8,0,0,0,12.26-10.3C183.18,177.07,164.6,164.44,144,157.68ZM56,100a52,52,0,1,1,52,52A52.06,52.06,0,0,1,56,100Zm188.25,43.07-4.66-2.69a23.6,23.6,0,0,0,0-8.76l4.66-2.69a8,8,0,0,0-8-13.86l-4.67,2.7a23.92,23.92,0,0,0-7.58-4.39V108a8,8,0,0,0-16,0v5.38a23.92,23.92,0,0,0-7.58,4.39l-4.67-2.7a8,8,0,1,0-8,13.86l4.66,2.69a23.6,23.6,0,0,0,0,8.76l-4.66,2.69a8,8,0,0,0,8,13.86l4.67-2.7a23.92,23.92,0,0,0,7.58,4.39V164a8,8,0,0,0,16,0v-5.38a23.92,23.92,0,0,0,7.58-4.39l4.67,2.7a7.92,7.92,0,0,0,4,1.07,8,8,0,0,0,4-14.93ZM216,144a8,8,0,1,1,8-8A8,8,0,0,1,216,144Z" />
                </svg>
              </span>
              <span className="truncate">Account Settings</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/forms/profile-settings/notification"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M208,192H48a8,8,0,0,1-6.88-12C47.71,168.6,56,147.81,56,112a72,72,0,0,1,144,0c0,35.82,8.3,56.6,14.9,68A8,8,0,0,1,208,192Z"
                    opacity="0.2"
                  />
                  <path d="M168,224a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,224ZM227.39,60.32a111.36,111.36,0,0,0-39.12-43.08,8,8,0,1,0-8.54,13.53,94.13,94.13,0,0,1,33.46,36.91,8,8,0,0,0,14.2-7.36ZM35.71,72a8,8,0,0,0,7.1-4.32A94.13,94.13,0,0,1,76.27,30.77a8,8,0,1,0-8.54-13.53A111.36,111.36,0,0,0,28.61,60.32,8,8,0,0,0,35.71,72Zm186.1,103.94A16,16,0,0,1,208,200H48a16,16,0,0,1-13.79-24.06C43.22,160.39,48,138.28,48,112a80,80,0,0,1,160,0C208,138.27,212.78,160.38,221.81,175.94ZM208,184c-10.64-18.27-16-42.49-16-72a64,64,0,0,0-128,0c0,29.52-5.38,53.74-16,72Z" />
                </svg>
              </span>
              <span className="truncate">Notification Preference</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/forms/profile-settings/profile"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M192,96a64,64,0,1,1-64-64A64,64,0,0,1,192,96Z"
                    opacity="0.2"
                  />
                  <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z" />
                </svg>
              </span>
              <span className="truncate">Personal Information</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/forms/newsletter"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,96l-78.55,56h-34.9L32,96l96-64Z"
                    opacity="0.2"
                  />
                  <path d="M228.44,89.34l-96-64a8,8,0,0,0-8.88,0l-96,64A8,8,0,0,0,24,96V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V96A8,8,0,0,0,228.44,89.34ZM128,41.61l81.91,54.61-67,47.78H113.11l-67-47.78ZM40,200V111.53l65.9,47a8,8,0,0,0,4.65,1.49h34.9a8,8,0,0,0,4.65-1.49l65.9-47V200Z" />
                </svg>
              </span>
              <span className="truncate">Newsletter</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/multi-step"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M240,56V192a8,8,0,0,1-8,8H72V152h56V104h56V56Z"
                    opacity="0.2"
                  />
                  <path d="M248,56a8,8,0,0,1-8,8H192v40a8,8,0,0,1-8,8H136v40a8,8,0,0,1-8,8H80v40a8,8,0,0,1-8,8H16a8,8,0,0,1,0-16H64V152a8,8,0,0,1,8-8h48V104a8,8,0,0,1,8-8h48V56a8,8,0,0,1,8-8h56A8,8,0,0,1,248,56Z" />
                </svg>
              </span>
              <span className="truncate">Multi Step</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/ecommerce/checkout"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M232,96v96a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V96Z"
                    opacity="0.2"
                  />
                  <path d="M224,48H32A16,16,0,0,0,16,64V192a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V64A16,16,0,0,0,224,48Zm0,16V88H32V64Zm0,128H32V104H224v88Zm-16-24a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h32A8,8,0,0,1,208,168Zm-64,0a8,8,0,0,1-8,8H120a8,8,0,0,1,0-16h16A8,8,0,0,1,144,168Z" />
                </svg>
              </span>
              <span className="truncate">Payment Checkout</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Tables
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/basic"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M208,56V200a8,8,0,0,1-8,8H56a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H200A8,8,0,0,1,208,56Z"
                    opacity="0.2"
                  />
                  <path d="M200,40H56A16,16,0,0,0,40,56V200a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V56A16,16,0,0,0,200,40Zm0,80H136V56h64ZM120,56v64H56V56ZM56,136h64v64H56Zm144,64H136V136h64v64Z" />
                </svg>
              </span>
              <span className="truncate">Basic</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/collapsible"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z"
                    opacity="0.2"
                  />
                  <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM165.66,98.34a8,8,0,0,1-11.32,11.32L128,83.31l-26.34,26.35A8,8,0,0,1,90.34,98.34l32-32a8,8,0,0,1,11.32,0Zm0,48a8,8,0,0,1,0,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32L128,172.69l26.34-26.35A8,8,0,0,1,165.66,146.34Z" />
                </svg>
              </span>
              <span className="truncate">Collapsible</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/enhanced"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M88,104v96H32V104Z" opacity="0.2" />
                  <path d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48ZM40,112H80v32H40Zm56,0H216v32H96ZM216,64V96H40V64ZM40,160H80v32H40Zm176,32H96V160H216v32Z" />
                </svg>
              </span>
              <span className="truncate">Enhanced</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/sticky-header"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,56V96H32V56a8,8,0,0,1,8-8H216A8,8,0,0,1,224,56Z"
                    opacity="0.2"
                  />
                  <path d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,16V88H40V56Zm0,144H40V104H216v96Z" />
                </svg>
              </span>
              <span className="truncate">Sticky Header</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/pagination"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M216,64V192H104V64Z" opacity="0.2" />
                  <path d="M224,128a8,8,0,0,1-8,8H104a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM104,72H216a8,8,0,0,0,0-16H104a8,8,0,0,0,0,16ZM216,184H104a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16ZM43.58,55.16,48,52.94V104a8,8,0,0,0,16,0V40a8,8,0,0,0-11.58-7.16l-16,8a8,8,0,0,0,7.16,14.32ZM79.77,156.72a23.73,23.73,0,0,0-9.6-15.95,24.86,24.86,0,0,0-34.11,4.7,23.63,23.63,0,0,0-3.57,6.46,8,8,0,1,0,15,5.47,7.84,7.84,0,0,1,1.18-2.13,8.76,8.76,0,0,1,12-1.59A7.91,7.91,0,0,1,63.93,159a7.64,7.64,0,0,1-1.57,5.78,1,1,0,0,0-.08.11L33.59,203.21A8,8,0,0,0,40,216H72a8,8,0,0,0,0-16H56l19.08-25.53A23.47,23.47,0,0,0,79.77,156.72Z" />
                </svg>
              </span>
              <span className="truncate">Pagination</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/tables/search"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M205.64,53.66,128,128,50.36,53.66A8,8,0,0,1,56,40H200A8,8,0,0,1,205.64,53.66ZM128,128,50.36,202.34A8,8,0,0,0,56,216H200a8,8,0,0,0,5.66-13.66Z"
                    opacity="0.2"
                  />
                  <path d="M211.18,196.56,139.57,128l71.61-68.56a1.59,1.59,0,0,1,.13-.13A16,16,0,0,0,200,32H56A16,16,0,0,0,44.69,59.31a1.59,1.59,0,0,1,.13.13L116.43,128,44.82,196.56a1.59,1.59,0,0,1-.13.13A16,16,0,0,0,56,224H200a16,16,0,0,0,11.32-27.31A1.59,1.59,0,0,1,211.18,196.56ZM56,48h0v0Zm144,0-72,68.92L56,48ZM56,208l72-68.92L200,208Z" />
                </svg>
              </span>
              <span className="truncate">Search</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Pages
          </h6>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/profile"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,128a95.76,95.76,0,0,1-31.8,71.37A72,72,0,0,0,128,160a40,40,0,1,0-40-40,40,40,0,0,0,40,40,72,72,0,0,0-64.2,39.37h0A96,96,0,1,1,224,128Z"
                    opacity="0.2"
                  />
                  <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24ZM74.08,197.5a64,64,0,0,1,107.84,0,87.83,87.83,0,0,1-107.84,0ZM96,120a32,32,0,1,1,32,32A32,32,0,0,1,96,120Zm97.76,66.41a79.66,79.66,0,0,0-36.06-28.75,48,48,0,1,0-59.4,0,79.66,79.66,0,0,0-36.06,28.75,88,88,0,1,1,131.52,0Z" />
                </svg>
              </span>
              <span className="truncate">Profile</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/welcome"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M230.19,78,200.7,103.17a5.09,5.09,0,0,0-1.66,5l8.84,37.46a5.2,5.2,0,0,1-7.77,5.57l-33.42-19.87a5.29,5.29,0,0,0-5.38,0l-33.42,19.87a5.2,5.2,0,0,1-7.77-5.57L129,108.22a5.09,5.09,0,0,0-1.66-5L97.81,78a5.12,5.12,0,0,1,3-9l38.88-3.29A5.21,5.21,0,0,0,144,62.57L159.2,27.14a5.24,5.24,0,0,1,9.6,0L184,62.57a5.21,5.21,0,0,0,4.36,3.12L227.23,69A5.12,5.12,0,0,1,230.19,78Z"
                    opacity="0.2"
                  />
                  <path d="M239.37,70.1A13.16,13.16,0,0,0,227.9,61l-37.22-3.15L176.16,24a13.24,13.24,0,0,0-24.31,0L137.33,57.86,100.1,61a13.13,13.13,0,0,0-7.49,23.06l28.16,24-8.43,35.73a13.1,13.1,0,0,0,5,13.58,13.25,13.25,0,0,0,14.63.7l32-19,32,19a13.25,13.25,0,0,0,14.63-.7,13.09,13.09,0,0,0,5-13.58l-8.43-35.73,28.15-24A13.07,13.07,0,0,0,239.37,70.1Zm-43.86,27a13.06,13.06,0,0,0-4.26,13l7.31,31-27.78-16.51a13.24,13.24,0,0,0-13.56,0L129.44,141l7.31-31a13,13,0,0,0-4.25-13L108.24,76.38l32.09-2.72a13.16,13.16,0,0,0,11-7.94L164,36.24l12.64,29.48a13.18,13.18,0,0,0,11,7.94l32.09,2.72ZM85.66,125.66l-56,56a8,8,0,0,1-11.32-11.32l56-56a8,8,0,0,1,11.32,11.32Zm16,56-56,56a8,8,0,0,1-11.32-11.32l56-56a8,8,0,0,1,11.32,11.32Zm72-11.32a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32-11.32l56-56A8,8,0,0,1,173.66,170.34Z" />
                </svg>
              </span>
              <span className="truncate">Welcome</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/coming-soon"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M72,160l24,24s-8,32-56,32C40,168,72,160,72,160Zm64-88H74.35a8,8,0,0,0-5.65,2.34L34.35,108.69a8,8,0,0,0,4.53,13.57L80,128Zm-8,104,5.74,41.12a8,8,0,0,0,13.57,4.53l34.35-34.35a8,8,0,0,0,2.34-5.65V120Z"
                    opacity="0.2"
                  />
                  <path d="M103.77,185.94C103.38,187.49,93.63,224,40,224a8,8,0,0,1-8-8c0-53.63,36.51-63.38,38.06-63.77a8,8,0,0,1,3.88,15.53c-.9.25-22.42,6.54-25.56,39.86C81.7,204.48,88,183,88.26,182a8,8,0,0,1,15.51,4Zm93-67.4L192,123.31v58.33A15.91,15.91,0,0,1,187.32,193L153,227.3A15.91,15.91,0,0,1,141.7,232a16.11,16.11,0,0,1-5.1-.83,15.94,15.94,0,0,1-10.78-12.92l-5.37-38.49L76.24,135.55l-38.47-5.37A16,16,0,0,1,28.7,103L63,68.68A15.91,15.91,0,0,1,74.36,64h58.33l4.77-4.77c26.68-26.67,58.83-27.82,71.41-27.07a16,16,0,0,1,15,15C224.6,59.71,223.45,91.86,196.78,118.54ZM40,114.34l37.15,5.18L116.69,80H74.36ZM91.32,128,128,164.68l57.45-57.45a76.46,76.46,0,0,0,22.42-59.16,76.64,76.64,0,0,0-59.11,22.47ZM176,139.31l-39.53,39.53L141.67,216,176,181.64Z" />
                </svg>
              </span>
              <span className="truncate">Coming soon</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/access-denied"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M224,168v40H152V168ZM92.69,48H40a8,8,0,0,0-8,8V80h96L98.34,50.34A8,8,0,0,0,92.69,48Z"
                    opacity="0.2"
                  />
                  <path d="M224,160h-8v-4a28,28,0,0,0-56,0v4h-8a8,8,0,0,0-8,8v40a8,8,0,0,0,8,8h72a8,8,0,0,0,8-8V168A8,8,0,0,0,224,160Zm-48-4a12,12,0,0,1,24,0v4H176Zm40,44H160V176h56Zm0-128H131.31L104,44.69A15.86,15.86,0,0,0,92.69,40H40A16,16,0,0,0,24,56V200.62A15.4,15.4,0,0,0,39.38,216h73.18a8,8,0,0,0,0-16H40V88H216v16a8,8,0,0,0,16,0V88A16,16,0,0,0,216,72ZM92.69,56l16,16H40V56Z" />
                </svg>
              </span>
              <span className="truncate">Access Denied</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/not-found"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M104,168a40,40,0,1,1-40-40A40,40,0,0,1,104,168Zm88-40a40,40,0,1,0,40,40A40,40,0,0,0,192,128Z"
                    opacity="0.2"
                  />
                  <path d="M237.2,151.87v0a47.1,47.1,0,0,0-2.35-5.45L193.26,51.8a7.82,7.82,0,0,0-1.66-2.44,32,32,0,0,0-45.26,0A8,8,0,0,0,144,55V80H112V55a8,8,0,0,0-2.34-5.66,32,32,0,0,0-45.26,0,7.82,7.82,0,0,0-1.66,2.44L21.15,146.4a47.1,47.1,0,0,0-2.35,5.45v0A48,48,0,1,0,112,168V96h32v72a48,48,0,1,0,93.2-16.13ZM76.71,59.75a16,16,0,0,1,19.29-1v73.51a47.9,47.9,0,0,0-46.79-9.92ZM64,200a32,32,0,1,1,32-32A32,32,0,0,1,64,200ZM160,58.74a16,16,0,0,1,19.29,1l27.5,62.58A47.9,47.9,0,0,0,160,132.25ZM192,200a32,32,0,1,1,32-32A32,32,0,0,1,192,200Z" />
                </svg>
              </span>
              <span className="truncate">Not Found</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/maintenance"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M245.66,129,217,157.66a8,8,0,0,1-11.3,0L168.1,120l-28,28-32-32,28-28L80,31.78a87.81,87.81,0,0,1,98.31,18l67.35,67.89A8,8,0,0,1,245.66,129Z"
                    opacity="0.2"
                  />
                  <path d="M251.34,112,183.88,44.08a96.1,96.1,0,0,0-135.77,0l-.09.09L34.25,58.4A8,8,0,0,0,45.74,69.53L59.47,55.35a79.92,79.92,0,0,1,18.71-13.9L124.68,88l-96,96a16,16,0,0,0,0,22.63l20.69,20.69a16,16,0,0,0,22.63,0l96-96,14.34,14.34h0L200,163.3a16,16,0,0,0,22.63,0l28.69-28.69A16,16,0,0,0,251.34,112ZM60.68,216,40,195.31l68-68L128.68,148ZM162.34,114.32,140,136.67,119.31,116l22.35-22.35a8,8,0,0,0,0-11.32L94.32,35a80,80,0,0,1,78.23,20.41l44.22,44.51L188,128.66l-14.34-14.34A8,8,0,0,0,162.34,114.32Zm49,37.66-12-12L228,111.25l12,12Z" />
                </svg>
              </span>
              <span className="truncate">Maintenance</span>
            </div>
          </a>
          <a
            className="group relative mx-3 my-0.5 flex items-center justify-between rounded-md px-3 py-2 font-medium capitalize text-gray-700 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90"
            href="/blank"
          >
            <div className="flex items-center truncate">
              <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                <svg
                  stroke="currentColor"
                  fill="currentColor"
                  strokeWidth={0}
                  viewBox="0 0 256 256"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M216,160l-56,56V160Z" opacity="0.2" />
                  <path d="M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H156.69A15.86,15.86,0,0,0,168,219.31L219.31,168A15.86,15.86,0,0,0,224,156.69V48A16,16,0,0,0,208,32ZM48,48H208V152H160a8,8,0,0,0-8,8v48H48ZM196.69,168,168,196.69V168Z" />
                </svg>
              </span>
              <span className="truncate">Blank</span>
            </div>
          </a>
          <h6 className="rizzui-title-h6 3xl:mt-7 mb-2 mt-6 truncate px-6 text-xs font-normal uppercase tracking-widest text-gray-500 2xl:px-8">
            Authentication
          </h6>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M168,100a60,60,0,1,1-60-60A60,60,0,0,1,168,100Z"
                      opacity="0.2"
                    />
                    <path d="M256,136a8,8,0,0,1-8,8H232v16a8,8,0,0,1-16,0V144H200a8,8,0,0,1,0-16h16V112a8,8,0,0,1,16,0v16h16A8,8,0,0,1,256,136Zm-57.87,58.85a8,8,0,0,1-12.26,10.3C165.75,181.19,138.09,168,108,168s-57.75,13.19-77.87,37.15a8,8,0,0,1-12.25-10.3c14.94-17.78,33.52-30.41,54.17-37.17a68,68,0,1,1,71.9,0C164.6,164.44,183.18,177.07,198.13,194.85ZM108,152a52,52,0,1,0-52-52A52.06,52.06,0,0,0,108,152Z" />
                  </svg>
                </span>
                Sign Up
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-up-1"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Modern Sign up</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-up-2"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Vintage Sign up</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-up-3"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Trendy Sign up</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-up-4"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Elegant Sign up</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-up-5"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Classic Sign up</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M216,56v58.77c0,84.18-71.31,112.07-85.54,116.8a7.54,7.54,0,0,1-4.92,0C111.31,226.86,40,199,40,114.79V56a8,8,0,0,1,8-8H208A8,8,0,0,1,216,56Z"
                      opacity="0.2"
                    />
                    <path d="M208,40H48A16,16,0,0,0,32,56v58.78c0,89.61,75.82,119.34,91,124.39a15.53,15.53,0,0,0,10,0c15.2-5.05,91-34.78,91-124.39V56A16,16,0,0,0,208,40Zm0,74.79c0,78.42-66.35,104.62-80,109.18-13.53-4.51-80-30.69-80-109.18V56H208ZM82.34,141.66a8,8,0,0,1,11.32-11.32L112,148.68l50.34-50.34a8,8,0,0,1,11.32,11.32l-56,56a8,8,0,0,1-11.32,0Z" />
                  </svg>
                </span>
                Sign In
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-in-1"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Modern Sign in</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-in-2"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Vintage Sign in</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-in-3"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Trendy Sign in</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-in-4"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Elegant Sign in</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/sign-in-5"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Classic Sign in</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M208,88H48a8,8,0,0,0-8,8V208a8,8,0,0,0,8,8H208a8,8,0,0,0,8-8V96A8,8,0,0,0,208,88Zm-80,72a20,20,0,1,1,20-20A20,20,0,0,1,128,160Z"
                      opacity="0.2"
                    />
                    <path d="M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-80-96a28,28,0,0,0-8,54.83V184a8,8,0,0,0,16,0V166.83A28,28,0,0,0,128,112Zm0,40a12,12,0,1,1,12-12A12,12,0,0,1,128,152Z" />
                  </svg>
                </span>
                Forgot Password
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/forgot-password-1"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Modern Forgot password</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/forgot-password-2"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Vintage Forgot password</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/forgot-password-3"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Trendy Forgot password</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/forgot-password-4"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Elegant Forgot password</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/forgot-password-5"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Classic Forgot password</span>
                </div>
              </a>
            </div>
          </div>
          <div
            role="collapse"
            aria-expanded="false"
            data-testid="collapse-parent"
            className="rizzui-collapse-root"
          >
            <div className="group relative mx-3 flex cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-100 lg:my-1 2xl:mx-5 2xl:my-2 dark:text-gray-700/90 dark:hover:text-gray-700">
              <span className="flex items-center">
                <span className="[&>svg]:h-[20px] [&>svg]:w-[20px] me-2 inline-flex h-5 w-5 items-center justify-center rounded-md text-gray-800 dark:text-gray-500 dark:group-hover:text-gray-700">
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth={0}
                    viewBox="0 0 256 256"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M224,56V184a8,8,0,0,1-8,8H156.53a8,8,0,0,0-6.86,3.88l-14.81,24.24a8,8,0,0,1-13.72,0l-14.81-24.24A8,8,0,0,0,99.47,192H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216A8,8,0,0,1,224,56Z"
                      opacity="0.2"
                    />
                    <path d="M116,120a12,12,0,1,1,12,12A12,12,0,0,1,116,120Zm56,12a12,12,0,1,0-12-12A12,12,0,0,0,172,132Zm60-76V184a16,16,0,0,1-16,16H156.53l-14.84,24.29a16,16,0,0,1-27.41-.06L99.47,200H40a16,16,0,0,1-16-16V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Zm-16,0H40V184H99.47a16.08,16.08,0,0,1,13.7,7.73L128,216l14.82-24.32A16.07,16.07,0,0,1,156.53,184H216ZM84,132a12,12,0,1,0-12-12A12,12,0,0,0,84,132Z" />
                  </svg>
                </span>
                OTP Pages
              </span>
              <svg
                stroke="currentColor"
                fill="currentColor"
                strokeWidth={3}
                viewBox="0 0 256 256"
                className="h-3.5 w-3.5 -rotate-90 text-gray-500 transition-transform duration-200 rtl:rotate-90"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z" />
              </svg>
            </div>
            <div className="rizzui-collapse-panel" style={{ display: "none" }}>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/otp-1"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Modern OTP page</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/otp-2"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Vintage OTP page</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/otp-3"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Trendy OTP page</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/otp-4"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Elegant OTP page</span>
                </div>
              </a>
              <a
                className="mx-3.5 mb-0.5 flex items-center justify-between rounded-md px-3.5 py-2 font-medium capitalize text-gray-500 transition-colors duration-200 last-of-type:mb-1 hover:bg-gray-100 hover:text-gray-900 lg:last-of-type:mb-2 2xl:mx-5"
                href="/auth/otp-5"
              >
                <div className="flex items-center truncate">
                  <span className="me-[18px] ms-1 inline-flex h-1 w-1 rounded-full bg-current opacity-40 transition-all duration-200" />{" "}
                  <span className="truncate">Classic OTP page</span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </ScrollArea>

      <ScrollArea className="flex-1 h-screen pr-4">
        <ScrollBar className="bg-red-500" />
        <div className="sticky top-0 inset-x-0 z-20 bg-white border-y px-4 sm:px-6 lg:px-8  dark:bg-neutral-800 dark:border-neutral-700">
          <div className="flex items-center py-2">
            {/* Navigation Toggle */}
            <button
              type="button"
              className="size-8 flex justify-center items-center gap-x-2 border border-gray-200 text-gray-800 hover:text-gray-500 rounded-lg focus:outline-none focus:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:border-neutral-700 dark:text-neutral-200 dark:hover:text-neutral-500 dark:focus:text-neutral-500"
              aria-haspopup="dialog"
              aria-expanded="true"
              aria-controls="hs-application-sidebar"
              aria-label="Toggle navigation"
              data-hs-overlay="#hs-application-sidebar"
            >
              <span className="sr-only">Toggle Navigation</span>
              <svg
                className="shrink-0 size-4"
                xmlns="http://www.w3.org/2000/svg"
                width={24}
                height={24}
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect width={18} height={18} x={3} y={3} rx={2} />
                <path d="M15 3v18" />
                <path d="m8 9 3 3-3 3" />
              </svg>
            </button>
            {/* End Navigation Toggle */}
            {/* Breadcrumb */}
            <ol className="ms-3 flex items-center whitespace-nowrap">
              <li className="flex items-center text-sm text-gray-800 dark:text-neutral-400">
                Application Layout
                <svg
                  className="shrink-0 mx-3 overflow-visible size-2.5 text-gray-400 dark:text-neutral-500"
                  width={16}
                  height={16}
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 1L10.6869 7.16086C10.8637 7.35239 10.8637 7.64761 10.6869 7.83914L5 14"
                    stroke="currentColor"
                    strokeWidth={2}
                    strokeLinecap="round"
                  />
                </svg>
              </li>
              <li
                className="text-sm font-semibold text-gray-800 truncate dark:text-neutral-400"
                aria-current="page"
              >
                Dashboard
              </li>
            </ol>
            {/* End Breadcrumb */}
          </div>
        </div>

        <div className="px-6 pt-6 font-sans">
          <h1 className="text-2xl font-bold mb-2">
            {new Date().getHours() > 12
              ? "Good Evening, houda."
              : "Good Morning, Houda."}
          </h1>
          <p className="text-gray-600 mb-6">
            Here s what s happening with your store today.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* In-store sales */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-600">In-store sales</span>
                <svg
                  className="w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 3h18v18H3z"
                  />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-2">$7,820.75</div>
              <div className="flex items-center text-sm">
                <span className="text-gray-600">5k orders</span>
                <span className="ml-2 text-green-500 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                  4.3%
                </span>
              </div>
            </div>
            {/* Website sales */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-600">Website sales</span>
                <svg
                  className="w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-2">$985,937.45</div>
              <div className="flex items-center text-sm">
                <span className="text-gray-600">21k orders</span>
                <span className="ml-2 text-green-500 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                  12.5%
                </span>
              </div>
            </div>
            {/* Discount */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-600">Discount</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width={24}
                  height={24}
                  color={"#000000"}
                  fill={"none"}
                >
                  <path
                    d="M7.72852 15.2861H12.7285M10.2271 12.7861H10.2364M10.2294 17.7861H10.2388"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M6.5 3.69682C9.53332 6.78172 14.5357 0.123719 17.4957 2.53998C19.1989 3.93028 18.6605 7 16.4494 9"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                  />
                  <path
                    d="M18.664 6.57831C19.6473 6.75667 19.8679 7.34313 20.1615 8.97048C20.4259 10.4361 20.5 12.1949 20.5 12.9436C20.4731 13.2195 20.3532 13.477 20.1615 13.687C18.1054 15.722 14.0251 19.565 11.9657 21.474C11.1575 22.1555 9.93819 22.1702 9.08045 21.5447C7.32407 20.0526 5.63654 18.366 3.98343 16.8429C3.3193 16.035 3.33487 14.8866 4.0585 14.1255C6.23711 11.9909 10.1793 8.33731 12.4047 6.31887C12.6278 6.1383 12.9012 6.02536 13.1942 6C13.6935 5.99988 14.5501 6.06327 15.3845 6.10896"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-2">$15,503.00</div>
              <div className="flex items-center text-sm">
                <span className="text-gray-600">6k orders</span>
              </div>
            </div>
            {/* Affiliate */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-600">Affiliate</span>
                <svg
                  className="w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-2">$3,982.53</div>
              <div className="flex items-center text-sm">
                <span className="text-gray-600">2.4 orders</span>
                <span className="ml-2 text-red-500 flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                  4.4%
                </span>
              </div>
            </div>
          </div>
        </div>
        {/* <div className="p-6">
          <ChartDasboard />
        </div> */}
      </ScrollArea>
    </div>
  );
}
